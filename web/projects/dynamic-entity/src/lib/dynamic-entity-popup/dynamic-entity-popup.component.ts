import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from "@angular/core";
import { CdkDragDrop } from "@angular/cdk/drag-drop";
import {
  Entity,
  FieldType,
  DynamicField,
  DynamicColumn,
  DynamicRow,
  DynamicSection,
  ViewType,
} from "../interfaces/dynamic-entity.interfaces";
import {
  FIELD_TYPES,
  SAMPLE_ENTITIES,
} from "../constants/field-types.constants";
import {
  SupportedLanguage,
  LanguageTranslations,
  LANGUAGE_TRANSLATIONS,
} from "../constants/language.constants";

@Component({
  selector: "lib-dynamic-entity-popup",
  templateUrl: "./dynamic-entity-popup.component.html",
  styleUrls: ["./dynamic-entity-popup.component.scss"],
})
export class DynamicEntityPopupComponent implements AfterViewInit {
  @Input() isVisible: boolean = false;
  @Input() entities: Entity[] = SAMPLE_ENTITIES;
  @Input() language: SupportedLanguage = "en";
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<void>();
  @Output() entitySelected = new EventEmitter<Entity>();
  @Output() fieldsBuilt = new EventEmitter<DynamicSection[]>();

  @ViewChild("sectionInput") sectionInput?: ElementRef<HTMLInputElement>;

  // Component state
  currentView: ViewType = "entities";
  selectedEntity: Entity | null = null;

  // Field builder data
  fieldTypes: FieldType[] = FIELD_TYPES;
  sections: DynamicSection[] = [];

  // Translation getter
  get translations(): LanguageTranslations {
    return LANGUAGE_TRANSLATIONS[this.language];
  }

  // Get translated entities
  get translatedEntities(): Entity[] {
    return this.entities.length > 0 ? SAMPLE_ENTITIES : [];
  }

  ngAfterViewInit(): void {
    // Focus input when editing section label
    if (this.sectionInput) {
      this.sectionInput.nativeElement.focus();
    }

    // Debug logging
    console.log("DynamicEntityPopup initialized:", {
      isVisible: this.isVisible,
      currentView: this.currentView,
      entitiesCount: this.entities.length,
      entities: this.entities,
    });
  }

  onClose(): void {
    this.close.emit();
    this.resetComponent();
  }

  onConfirm(): void {
    this.confirm.emit();
    if (this.currentView === "builder") {
      this.fieldsBuilt.emit(this.sections);
    }
  }

  onOverlayClick(): void {
    this.onClose();
  }

  selectEntity(entity: Entity): void {
    this.selectedEntity = entity;
    this.entitySelected.emit(entity);
    this.slideToBuilder();
  }

  goBackToEntities(): void {
    this.slideToEntities();
  }

  private slideToBuilder(): void {
    this.currentView = "builder";
    this.initializeBuilder();
  }

  private slideToEntities(): void {
    this.currentView = "entities";
  }

  private initializeBuilder(): void {
    if (this.sections.length === 0) {
      this.addSection();
    }
  }

  private resetComponent(): void {
    this.currentView = "entities";
    this.selectedEntity = null;
    this.sections = [];
  }

  // Section management
  addSection(): void {
    const newSection: DynamicSection = {
      id: this.generateId(),
      label: `${this.translations.addSection} ${this.sections.length + 1}`,
      rows: [],
    };

    this.sections.push(newSection);
    this.addRowToSection(newSection);
  }

  removeSection(index: number): void {
    this.sections.splice(index, 1);
  }

  editSectionLabel(section: DynamicSection): void {
    section.isEditing = true;
    setTimeout(() => {
      if (this.sectionInput) {
        this.sectionInput.nativeElement.focus();
        this.sectionInput.nativeElement.select();
      }
    });
  }

  saveSectionLabel(section: DynamicSection): void {
    section.isEditing = false;
  }

  // Row management
  addRowToSection(section: DynamicSection): void {
    const newRow: DynamicRow = {
      id: this.generateId(),
      columns: [],
    };

    section.rows.push(newRow);
    this.addColumnToRow(newRow);
  }

  removeRowFromSection(section: DynamicSection, rowIndex: number): void {
    section.rows.splice(rowIndex, 1);
  }

  // Column management
  addColumnToRow(row: DynamicRow): void {
    const newColumn: DynamicColumn = {
      id: this.generateId(),
      width: Math.floor(12 / (row.columns.length + 1)),
    };

    row.columns.push(newColumn);
    this.redistributeColumnWidths(row);
  }

  removeColumnFromRow(row: DynamicRow, columnIndex: number): void {
    row.columns.splice(columnIndex, 1);
    this.redistributeColumnWidths(row);
  }

  private redistributeColumnWidths(row: DynamicRow): void {
    const equalWidth = Math.floor(12 / row.columns.length);
    row.columns.forEach((column) => {
      column.width = equalWidth;
    });
  }

  // Field management
  onFieldDrop(
    event: CdkDragDrop<DynamicColumn[]>,
    column: DynamicColumn
  ): void {
    if (event.previousContainer !== event.container) {
      const fieldType = event.item.data as FieldType;
      this.createFieldInColumn(column, fieldType);
    }
  }

  private createFieldInColumn(
    column: DynamicColumn,
    fieldType: FieldType
  ): void {
    const translatedFieldName = this.getTranslatedFieldType(
      fieldType.type,
      this.translations
    );
    const newField: DynamicField = {
      id: this.generateId(),
      fieldType: fieldType,
      label: translatedFieldName,
      required: false,
      placeholder: `${
        this.language === "ar" ? "أدخل" : "Enter"
      } ${translatedFieldName.toLowerCase()}`,
    };

    column.field = newField;
  }

  removeFieldFromColumn(column: DynamicColumn): void {
    column.field = undefined;
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 9);
  }

  getTranslatedFieldType(
    fieldType: string,
    translations: LanguageTranslations
  ): string {
    switch (fieldType) {
      case "text":
        return translations.text;
      case "number":
        return translations.number;
      case "email":
        return translations.email;
      case "date":
        return translations.date;
      case "select":
        return translations.select;
      case "textarea":
        return translations.textarea;
      case "checkbox":
        return translations.checkbox;
      case "file":
        return translations.file;
      case "color":
        return translations.color;
      default:
        return fieldType;
    }
  }
}
