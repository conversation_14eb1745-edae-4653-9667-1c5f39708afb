import { Component, Input } from "@angular/core";
import { SupportedLanguage } from "./constants/language.constants";

@Component({
  selector: "lib-dynamic-entity-float-widget",
  template: `
    <lib-dynamic-entity-float-button (buttonClick)="onFloatButtonClick()">
    </lib-dynamic-entity-float-button>

    <lib-dynamic-entity-popup
      [isVisible]="isPopupVisible"
      [language]="language"
      (close)="onPopupClose()"
      (confirm)="onPopupConfirm()"
    >
    </lib-dynamic-entity-popup>
  `,
  styles: [],
})
export class DynamicEntityFloatWidgetComponent {
  @Input() language: SupportedLanguage = "en";
  isPopupVisible: boolean = false;

  onFloatButtonClick(): void {
    this.isPopupVisible = true;
  }

  onPopupClose(): void {
    this.isPopupVisible = false;
  }

  onPopupConfirm(): void {
    // Handle confirm action here
    console.log("Popup confirmed");
    this.isPopupVisible = false;
  }
}
