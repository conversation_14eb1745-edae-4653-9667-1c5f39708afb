export interface Entity {
    id: string;
    name: string;
    customFieldsCount: number;
    description?: string;
}

export interface FieldType {
    id: string;
    name: string;
    icon: string;
    type:
        | 'text'
        | 'number'
        | 'email'
        | 'date'
        | 'select'
        | 'textarea'
        | 'checkbox'
        | 'file'
        | 'color';
}

export interface DynamicField {
    id: string;
    fieldType: FieldType;
    label: string;
    required: boolean;
    placeholder?: string;
    options?: string[];
}

export interface DynamicColumn {
    id: string;
    field?: DynamicField;
    width: number; // 1-12 for grid system
}

export interface DynamicRow {
    id: string;
    columns: DynamicColumn[];
}

export interface DynamicSection {
    id: string;
    label: string;
    rows: DynamicRow[];
    isEditing?: boolean;
}

export type ViewType = 'entities' | 'builder';
