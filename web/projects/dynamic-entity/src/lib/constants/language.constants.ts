export type SupportedLanguage = "en" | "ar";

export interface LanguageTranslations {
  // General UI
  close: string;
  confirm: string;
  cancel: string;
  save: string;
  edit: string;
  delete: string;
  add: string;

  // Entity related
  selectEntity: string;
  entityBuilder: string;
  dynamicEntityBuilder: string;
  selectedEntity: string;
  customFieldsCount: string;

  // Field Builder
  fieldBuilder: string;
  addSection: string;
  sectionName: string;
  addRow: string;
  addColumn: string;
  fieldTypes: string;
  dragFieldHere: string;

  // Field Types
  text: string;
  number: string;
  email: string;
  date: string;
  select: string;
  textarea: string;
  checkbox: string;
  file: string;
  color: string;

  // Field Properties
  fieldLabel: string;
  placeholder: string;
  required: string;
  optional: string;

  // Validation Messages
  fieldRequired: string;
  invalidEmail: string;
  invalidDate: string;

  // Actions
  buildFields: string;
  previewForm: string;
  exportForm: string;
}

export const ENGLISH_TRANSLATIONS: LanguageTranslations = {
  // General UI
  close: "Close",
  confirm: "Confirm",
  cancel: "Cancel",
  save: "Save",
  edit: "Edit",
  delete: "Delete",
  add: "Add",

  // Entity related
  selectEntity: "Select Entity",
  entityBuilder: "Entity Builder",
  dynamicEntityBuilder: "Dynamic Entity Builder",
  selectedEntity: "Selected Entity",
  customFieldsCount: "Custom Fields",

  // Field Builder
  fieldBuilder: "Field Builder",
  addSection: "Add Section",
  sectionName: "Section Name",
  addRow: "Add Row",
  addColumn: "Add Column",
  fieldTypes: "Field Types",
  dragFieldHere: "Drag a field here",

  // Field Types
  text: "Text",
  number: "Number",
  email: "Email",
  date: "Date",
  select: "Select",
  textarea: "Textarea",
  checkbox: "Checkbox",
  file: "File",
  color: "Color",

  // Field Properties
  fieldLabel: "Field Label",
  placeholder: "Placeholder",
  required: "Required",
  optional: "Optional",

  // Validation Messages
  fieldRequired: "This field is required",
  invalidEmail: "Please enter a valid email address",
  invalidDate: "Please enter a valid date",

  // Actions
  buildFields: "Build Fields",
  previewForm: "Preview Form",
  exportForm: "Export Form",
};

export const ARABIC_TRANSLATIONS: LanguageTranslations = {
  // General UI
  close: "إغلاق",
  confirm: "تأكيد",
  cancel: "إلغاء",
  save: "حفظ",
  edit: "تعديل",
  delete: "حذف",
  add: "إضافة",

  // Entity related
  selectEntity: "اختيار الوحده",
  entityBuilder: "التطبيقات",
  dynamicEntityBuilder: "منشئ الوحده",
  selectedEntity: "الوحده المحدده",
  customFieldsCount: "الحقول المخصصة",

  // Field Builder
  fieldBuilder: "منشئ الحقول",
  addSection: "إضافة قسم",
  sectionName: "اسم القسم",
  addRow: "إضافة صف",
  addColumn: "إضافة عمود",
  fieldTypes: "أنواع الحقول",
  dragFieldHere: "اسحب حقلاً هنا",

  // Field Types
  text: "نص",
  number: "رقم",
  email: "بريد إلكتروني",
  date: "تاريخ",
  select: "اختيار",
  textarea: "منطقة نص",
  checkbox: "مربع اختيار",
  file: "ملف",
  color: "لون",

  // Field Properties
  fieldLabel: "تسمية الحقل",
  placeholder: "نص توضيحي",
  required: "مطلوب",
  optional: "اختياري",

  // Validation Messages
  fieldRequired: "هذا الحقل مطلوب",
  invalidEmail: "يرجى إدخال عنوان بريد إلكتروني صحيح",
  invalidDate: "يرجى إدخال تاريخ صحيح",

  // Actions
  buildFields: "بناء الحقول",
  previewForm: "معاينة النموذج",
  exportForm: "تصدير النموذج",
};

export const LANGUAGE_TRANSLATIONS: Record<
  SupportedLanguage,
  LanguageTranslations
> = {
  en: ENGLISH_TRANSLATIONS,
  ar: ARABIC_TRANSLATIONS,
};
