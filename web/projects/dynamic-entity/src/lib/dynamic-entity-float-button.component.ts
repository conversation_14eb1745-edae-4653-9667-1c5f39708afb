import { Component, EventEmitter, Output } from "@angular/core";

@Component({
  selector: "lib-dynamic-entity-float-button",
  template: `
    <div class="dynamic-entity-float-button" (click)="onButtonClick()">
      <div class="float-button-icon">
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 2L2 7L12 12L22 7L12 2Z"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M2 17L12 22L22 17"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M2 12L12 17L22 12"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
  `,
  styles: [
    `
      .dynamic-entity-float-button {
        position: fixed;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        width: 56px;
        height: 56px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        z-index: 1000;
        border: 2px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
      }

      .dynamic-entity-float-button:hover {
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
      }

      .dynamic-entity-float-button:active {
        transform: translateY(-50%) scale(0.95);
      }

      .float-button-icon {
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .float-button-icon svg {
        width: 24px;
        height: 24px;
      }
    `,
  ],
})
export class DynamicEntityFloatButtonComponent {
  @Output() buttonClick = new EventEmitter<void>();

  onButtonClick(): void {
    this.buttonClick.emit();
  }
}
