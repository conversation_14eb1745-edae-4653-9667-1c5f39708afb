// Test file to verify language constants work correctly
import { LANGUAGE_TRANSLATIONS, SupportedLanguage } from './constants/language.constants';
import { getTranslatedEntities } from './constants/field-types.constants';

// Test English translations
const englishTranslations = LANGUAGE_TRANSLATIONS['en'];
console.log('English translations:', {
  close: englishTranslations.close,
  dynamicEntityBuilder: englishTranslations.dynamicEntityBuilder,
  userProfile: englishTranslations.userProfile
});

// Test Arabic translations
const arabicTranslations = LANGUAGE_TRANSLATIONS['ar'];
console.log('Arabic translations:', {
  close: arabicTranslations.close,
  dynamicEntityBuilder: arabicTranslations.dynamicEntityBuilder,
  userProfile: arabicTranslations.userProfile
});

// Test translated entities
const englishEntities = getTranslatedEntities('en');
const arabicEntities = getTranslatedEntities('ar');

console.log('English entities:', englishEntities[0]);
console.log('Arabic entities:', arabicEntities[0]);

export { englishTranslations, arabicTranslations, englishEntities, arabicEntities };
