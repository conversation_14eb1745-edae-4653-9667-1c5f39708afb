<app-page pageTitle="{{ 'translate_innovation_details' | translate }}">
    <!-- Tools -->
    <ng-container tools *appHasPermissionId="permissionList.innovation">
        <!-- List -->
        <a class="btn btn-sm btn-success" [routerLink]="['', 'innovation']">
            <i class="fa-light fa-share"></i>
            <span class="hidden md:inline">
                {{ 'translate_innovations_list' | translate }}
            </span>
        </a>

        <!-- New -->
        <a
            class="btn btn-sm btn-primary"
            [routerLink]="['', 'innovation', 'new']"
        >
            <i class="fa-light fa-plus"></i>
            <span class="hidden md:inline">
                {{ 'translate_add_new' | translate }}
            </span>
        </a>

        <!-- Edit -->
        <a
            class="btn btn-sm btn-info"
            *ngIf="innovation"
            [routerLink]="['', 'innovation', 'edit', innovation.id]"
            [appTooltip]="'translate_edit' | translate"
        >
            <i class="fa-light fa-edit"></i>
            <span class="hidden md:inline">
                {{ 'translate_edit' | translate }}
            </span>
        </a>
    </ng-container>

    <div content class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <!-- Details -->
        <app-content [contentTitle]="innovation?.title">
            <table content>
                <tbody>
                    <!-- Innovation Title -->
                    <tr>
                        <td class="whitespace-nowrap">
                            {{ 'translate_innovation_title' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="innovation">
                                {{ innovation.title }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Description -->
                    <tr>
                        <td>
                            {{ 'translate_description' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="innovation">
                                {{ innovation.details }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Innovator -->
                    <tr>
                        <td>
                            {{ 'translate_innovator' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="innovation">
                                {{ innovation.innovator.name }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Suggestion Number -->
                    <tr>
                        <td>
                            {{ 'translate_suggestion_number' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="innovation">
                                {{ innovation.suggestionNumber }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Year -->
                    <tr>
                        <td>
                            {{ 'translate_year' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="innovation">
                                {{ innovation.year }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Impact -->
                    <tr>
                        <td>
                            {{ 'translate_innovation_result' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="innovation">
                                {{ innovation.impact }}
                            </ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>

        <div>
            <div class="grid gap-3">
                <!-- Activities -->
                <app-content
                    contentTitle="{{ 'translate_activities' | translate }}"
                >
                    <!-- Content -->
                    <app-content-loading content [isLoading]="!innovation">
                        <table content>
                            <thead>
                                <th>{{ 'translate_name' | translate }}</th>
                                <th>{{ 'translate_year' | translate }}</th>
                            </thead>
                            <tbody>
                                <tr
                                    *ngFor="
                                        let activity of innovation?.activities
                                    "
                                >
                                    <td>
                                        <ng-container
                                            *appWaitUntilLoaded="innovation"
                                        >
                                            {{ activity.name }}
                                        </ng-container>
                                    </td>
                                    <td>
                                        <ng-container
                                            *appWaitUntilLoaded="innovation"
                                        >
                                            {{ activity.year }}
                                        </ng-container>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </app-content-loading>
                </app-content>

                <!-- Documentation  -->
                <app-content
                    contentTitle="{{ 'translate_documentation' | translate }}"
                >
                    <!-- Content -->
                    <app-content-loading content [isLoading]="!innovation">
                        <table content>
                            <tbody>
                                <tr>
                                    <td>
                                        <ng-container
                                            *appWaitUntilLoaded="innovation"
                                        >
                                            <a
                                                href="javascript:void(0)"
                                                (click)="
                                                    download(
                                                        innovation?.id,
                                                        innovation.contentType
                                                    )
                                                "
                                            >
                                                {{
                                                    innovation.documentationType
                                                }}
                                            </a>
                                        </ng-container>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </app-content-loading>
                </app-content>
            </div>
        </div>
    </div>
</app-page>
