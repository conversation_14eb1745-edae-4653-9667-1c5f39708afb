import { Component, NgModuleRef, <PERSON>Zone } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ModalService } from 'mnm-webapp';
import { environment } from '@masar/env/environment';
import { Subject } from 'rxjs';
import { first } from 'rxjs/operators';
import { FileViewerComponent } from '@masar/features/masar/components';
import { functions } from '@masar/common/misc/functions';
import { permissionList } from '@masar/common/constants';
import { Innovation } from '@masar/common/models';
import { InnovationService } from '../innovation.service';

@Component({
    selector: 'app-detail',
    templateUrl: './detail.component.html',
})
export class DetailComponent {
    public readonly permissionList = permissionList;
    public readonly environment = environment;

    public innovation: Innovation;
    public innovationId: string;

    public constructor(
        private innovationService: InnovationService,
        private activatedRoute: ActivatedRoute,
        private modalService: ModalService,
        private moduleRef: NgModuleRef<any>,
        private ngZone: NgZone
    ) {
        this.activatedRoute.params.pipe(first()).subscribe(params => {
            {
                this.innovationId = params.id;
                this.innovationService
                    .get(this.innovationId)
                    .subscribe(item => (this.innovation = item));
            }
        });
    }

    public download(id: string, type: string): void {
        this.innovationService.downloadAttachment(id).subscribe(file => {
            if (type === 'application/pdf' || type.split('/')[0] === 'image') {
                const src = URL.createObjectURL(file);
                this.showFileModal(src);
            } else {
                this.ngZone.runOutsideAngular(() => {
                    functions.downloadBlobIntoFile(file);
                });
            }
        });
    }

    public showFileModal(src: string): void {
        let subject = new Subject();
        // noinspection JSIgnoredPromiseFromCall
        this.modalService.show(FileViewerComponent, {
            moduleRef: this.moduleRef,
            onDismiss: () => {
                subject.next();
                subject.complete();
            },
            beforeInit: c => {
                c.src = src;
            },
            size: {
                width: '100%',
                height: '100%',
            },
        });
    }
}
