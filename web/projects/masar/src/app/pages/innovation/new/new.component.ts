import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { Subject } from 'rxjs';
import { finalize, first } from 'rxjs/operators';
import { InnovatorService } from '@masar/pages/innovator/innovator.service';
import { permissionList } from '@masar/common/constants';
import { Innovation } from '@masar/common/models';
import {
    AppSettingFetcherService,
    HelperService,
    MiscApiService,
    PermissionService,
} from '@masar/core/services';
import { MnmFormState } from '@masar/shared/components';
import { InnovationService } from '../innovation.service';
import { fields } from './fields';

@Component({
    templateUrl: './new.component.html',
})
export class NewComponent implements OnInit, On<PERSON><PERSON>roy {
    public readonly permissionList = permissionList;

    public isSubmitting = false;
    public mode: 'new' | 'edit';
    public formState: MnmFormState;
    public isInnovator: boolean;

    private hasInnovationPermission: boolean;
    private unsubscribeAll = new Subject();

    public constructor(
        private router: Router,
        private permissionService: PermissionService,
        private notificationService: NotificationService,
        private innovationService: InnovationService,
        private translateService: TranslateService,
        private activatedRoute: ActivatedRoute,
        private miscApiService: MiscApiService,
        // FIXME: Move this service to shared service
        private innovatorService: InnovatorService,
        private readonly helperService: HelperService,
        appSettingFetcherService: AppSettingFetcherService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(appSettingFetcherService), fb);

        this.loadSelectItems();
    }

    public ngOnInit(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[0].path) {
                case 'new':
                    this.mode = 'new';

                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            const id = params.id;
                            this.innovationService
                                .get(id, true)
                                .subscribe(item => {
                                    this.fillForm(item);
                                });
                        });
                    break;
            }
        });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const observable =
            this.mode === 'new'
                ? this.innovationService.create(
                      this.formState.group.getRawValue()
                  )
                : this.innovationService.update(
                      this.formState.group.getRawValue()
                  );

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(innovation => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                if (!this.hasInnovationPermission) {
                    this.router.navigate(['', 'innovator', 'profile']).then();
                }

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'innovation'],
                    innovation.id
                );
            });
    }

    private fillForm(item: Innovation): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }

    private loadSelectItems(): void {
        this.miscApiService.setMiscItems(this.formState, [
            ['activity', 'activities', undefined, true],
        ]);

        this.permissionService
            .ensureUserHasPermission(this.permissionList.innovation)
            .subscribe(hasInnovationPermission => {
                this.hasInnovationPermission = hasInnovationPermission;
                const innovatorControl =
                    this.formState.group.controls['innovator'];

                if (hasInnovationPermission) {
                    this.miscApiService.setMiscItems(this.formState, [
                        ['innovator', 'innovator', undefined, true],
                    ]);

                    innovatorControl.enable();
                    return;
                }

                this.innovatorService.currentInnovator().subscribe(items => {
                    if (!items) {
                        this.isInnovator = false;
                        return;
                    }

                    this.formState.get('innovator').items = [items];
                    innovatorControl.setValue(items);
                });
            });
    }
}
