import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { permissionList } from '@masar/common/constants';
import { DetailComponent } from './detail/detail.component';
import { InnovationComponent } from './innovation.component';
import { ListComponent } from './list/list.component';
import { NewComponent } from './new/new.component';

const routes: Routes = [
    {
        path: '',
        component: InnovationComponent,
        children: [
            {
                path: '',
                component: ListComponent,
                data: {
                    title: 'translate_innovations',
                    breadcrumb: ['translate_innovations'],
                    permissionId: permissionList.innovation,
                },
            },
            {
                path: 'new',
                component: NewComponent,
                data: {
                    title: 'translate_new_innovation',
                    breadcrumb: ['translate_innovations', 'translate_add_new'],
                    permissionId: permissionList.innovator,
                },
            },
            {
                path: 'edit/:id',
                component: NewComponent,
                data: {
                    title: 'translate_edit_innovation',
                    breadcrumb: ['translate_innovations', 'translate_edit'],
                    permissionId: permissionList.innovation,
                },
            },
            {
                path: 'detail/:id',
                component: DetailComponent,
                data: {
                    title: 'translate_innovation_details',
                    breadcrumb: ['translate_innovations', 'translate_details'],
                    permissionId: permissionList.innovator,
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class InnovationRoutingModule {}
