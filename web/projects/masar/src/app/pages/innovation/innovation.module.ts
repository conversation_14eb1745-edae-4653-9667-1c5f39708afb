import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InnovationComponent } from './innovation.component';
import { SharedModule } from '@masar/shared/shared.module';
import { InnovationRoutingModule } from './innovation.routing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { MasarModule } from '@masar/features/masar/masar.module';
import { NgSelectModule } from '@ng-select/ng-select';
import { DetailComponent } from './detail/detail.component';
import { ListComponent } from './list/list.component';
import { NewComponent } from './new/new.component';
import { InnovationService } from './innovation.service';
import { InnovatorService } from '@masar/pages/innovator/innovator.service';
import { TranslationModule } from '@ng-omar/translation';

@NgModule({
    declarations: [
        InnovationComponent,
        ListComponent,
        NewComponent,
        DetailComponent,
    ],
    imports: [
        CommonModule,
        SharedModule,
        InnovationRoutingModule,
        TranslationModule,
        FormsModule,
        ReactiveFormsModule,
        SweetAlert2Module,
        MasarModule,
        NgSelectModule,
    ],
    providers: [InnovationService, InnovatorService],
})
export class InnovationModule {}
