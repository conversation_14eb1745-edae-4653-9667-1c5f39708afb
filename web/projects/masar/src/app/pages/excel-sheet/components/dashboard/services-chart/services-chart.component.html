<div class="mb-4 flex items-center justify-between">
    <h1 class="text-xl font-bold">عدد المتعاملين علي مستوي المركز</h1>

    <!-- Year Field -->
    <ng-select
        name="year"
        id="year"
        class="w-72"
        placeholder="فرز بالسنة"
        [items]="years"
        [(ngModel)]="selectedYears"
        (change)="setBarCharData()"
        [multiple]="true"
    >
    </ng-select>

    <!-- Center Field -->
    <select
        name="center"
        id="center"
        class="bg-blur rounded-full text-center"
        [(ngModel)]="selectedCenterId"
        (change)="setBarCharData()"
    >
        <option value="0" selected disabled>المراكز</option>
        <option *ngFor="let center of centers" [value]="center.id">
            {{ center.name }}
        </option>
    </select>
</div>

<div style="display: block">
    <canvas
        baseChart
        [chartType]="chartType"
        [datasets]="chartData"
        [labels]="chartLabels"
        [options]="chartOptions"
        [plugins]="chartPlugins"
        [legend]="chartLegend"
    >
    </canvas>
</div>
