import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ExcelSheetService } from '../../excel-sheet.service';
import { finalize } from 'rxjs/operators';

export interface Center {
    id: number;
    name: string;
}

@Component({
    selector: 'app-centers',
    templateUrl: './centers.component.html',
})
export class CentersComponent implements OnInit {
    public centers: Center[] = [];
    public isLoadingCenters = false;
    public isSubmittingCenters = false;

    public centerForm = this.fb.group({
        name: [null, [Validators.required, Validators.minLength(3)]],
    });

    public constructor(
        private readonly fb: FormBuilder,
        private readonly api: ExcelSheetService
    ) {}

    public ngOnInit(): void {
        this.getCenters();
    }

    public createCenter(): void {
        if (this.centerForm.invalid) return;
        this.isSubmittingCenters = true;

        const name = this.centerForm.controls['name']
            .value as unknown as string;

        this.api
            .createCenter(name)
            .pipe(finalize(() => (this.isSubmittingCenters = false)))
            .subscribe(() => {
                this.centerForm.reset();
                this.getCenters();
            });
    }

    public getCenters(): void {
        if (this.isLoadingCenters) return;

        this.isLoadingCenters = true;

        this.api
            .getCenters()
            .pipe(finalize(() => (this.isLoadingCenters = false)))
            .subscribe(centers => (this.centers = centers));
    }

    public deleteCenter(id: number): void {
        this.api.deleteCenter(id).subscribe(() => this.getCenters());
    }
}
