<div class="h-full items-stretch">
    <!-- Create -->
    <form
        class="mb-2 flex flex-col items-center justify-center px-2 py-4"
        [formGroup]="serviceForm"
        (ngSubmit)="createService()"
        autocomplete="off"
    >
        <h2 class="mb-3 px-2 text-xl font-bold">إنشاء خدمة جديدة</h2>
        <input
            type="text"
            id="service_name"
            formControlName="name"
            class="bg-blur mb-2 w-full rounded-full border-none"
            placeholder="اسم الخدمة"
        />
        <button
            type="submit"
            class="bg-blur w-full rounded-full p-3"
            [disabled]="isSubmittingServices"
        >
            انشاء
        </button>
    </form>

    <div class="w-ful h-0.5 bg-slate-300"></div>

    <!-- List -->
    <div class="flex flex-col items-center justify-center border-e px-2 py-4">
        <div class="w-full overflow-auto">
            <h2 class="mb-3 px-2 text-xl font-bold">الخدمات</h2>

            <!-- Item -->
            <div
                class="bg-blur mb-2 flex items-center justify-between rounded-full p-4"
                *ngFor="let service of services"
            >
                <div class="text-sm">
                    <p class="line-clamp-1" [title]="service.name">
                        {{ service.name }}
                    </p>
                </div>
                <button
                    class="rounded bg-red-500 px-2 text-xs text-white"
                    type="button"
                    (click)="deleteService(service.id)"
                >
                    X
                </button>
            </div>

            <!-- No Items -->
            <ng-container *ngIf="!services.length">
                <!-- Is Loading -->
                <div class="loading text-center" *ngIf="isLoadingServices">
                    <div class="lds-dual-ring"></div>
                </div>

                <!-- No Items Found -->
                <p
                    class="my-2 text-sm italic text-gray-400"
                    *ngIf="!isLoadingServices"
                >
                    لا يوجد عناصر
                </p>
            </ng-container>
        </div>
    </div>
</div>
