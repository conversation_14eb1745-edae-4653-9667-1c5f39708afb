<div class="max-h-full overflow-auto p-2">
    <div class="bg-blur flex justify-between rounded-none p-4">
        <!-- Department Field -->
        <select
            name="department"
            id="department"
            class="bg-blur w-52 rounded-full border-none px-4 py-1 text-center"
            [(ngModel)]="selectedDepartmentId"
            (change)="getDataEntries()"
        >
            <option value="0" selected disabled>الإدارات</option>
            <option
                *ngFor="let department of departments"
                [value]="department.id"
            >
                {{ department.name }}
            </option>
        </select>
    </div>

    <div class="p-4">
        <app-summary
            class="mb-4 block"
            [centers]="centers"
            [dataEntries]="dataEntries"
            [isLoadingEntries]="isLoadingEntries"
        ></app-summary>

        <div class="bg-blur mb-4 p-4">
            <app-centers-chart
                [centers]="centers"
                [dataEntries]="dataEntries"
            ></app-centers-chart>
        </div>

        <div class="bg-blur p-4">
            <app-services-chart
                [services]="services"
                [centers]="centers"
                [dataEntries]="dataEntries"
            ></app-services-chart>
        </div>
    </div>
</div>
