import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { finalize } from 'rxjs/operators';
import { ExcelSheetService } from '../../excel-sheet.service';

export interface Service {
    id: number;
    name: string;
}

@Component({
    selector: 'app-services',
    templateUrl: './services.component.html',
})
export class ServicesComponent implements OnInit {
    public services: Service[] = [];
    public isLoadingServices = false;
    public isSubmittingServices = false;

    public serviceForm = this.fb.group({
        name: [null, [Validators.required, Validators.minLength(3)]],
    });

    public constructor(
        private readonly fb: FormBuilder,
        private readonly api: ExcelSheetService
    ) {}

    public ngOnInit(): void {
        this.getServices();
    }

    public createService(): void {
        if (this.serviceForm.invalid) return;
        const name = this.serviceForm.controls['name']
            .value as unknown as string;

        this.isSubmittingServices = true;
        this.api
            .createService(name)
            .pipe(finalize(() => (this.isSubmittingServices = false)))
            .subscribe(() => {
                this.serviceForm.reset();
                this.getServices();
            });
    }

    public getServices(): void {
        if (this.isLoadingServices) return;

        this.isLoadingServices = true;

        this.api
            .getServices()
            .pipe(finalize(() => (this.isLoadingServices = false)))
            .subscribe(services => (this.services = services));
    }

    public deleteService(id: number): void {
        this.api.deleteService(id).subscribe(() => this.getServices());
    }
}
