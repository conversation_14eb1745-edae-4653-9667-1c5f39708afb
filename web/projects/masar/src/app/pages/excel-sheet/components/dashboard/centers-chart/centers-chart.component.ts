import { Component, Input, OnChanges } from '@angular/core';
import { ChartDataSets, ChartOptions, ChartType } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Center } from '../../centers/centers.component';
import { DataEntry } from '../../data-entry/data-entry.component';
import { getColor, getYears } from '../../../utils';
import { currentColor, expectedColor, months } from '../../../constants';
import { linearRegression } from 'simple-statistics';
import {
    Label,
    PluginServiceGlobalRegistrationAndOptions,
} from 'ng2-charts/lib/base-chart.directive';

@Component({
    selector: 'app-centers-chart',
    templateUrl: './centers-chart.component.html',
})
export class CentersChartComponent implements OnChanges {
    @Input() public dataEntries: DataEntry[] = [];

    @Input() public centers: Center[] = [];

    public selectedCenterId!: string;

    public selectedYear!: number;

    public years!: number[];

    public monthsMistakePercentage?: { value: string; color: string }[];

    public chartLabels: Label[] = [];
    public chartType: ChartType = 'bar';
    public chartLegend = true;
    public chartPlugins: PluginServiceGlobalRegistrationAndOptions[] = [
        ChartDataLabels,
    ];
    public chartData: ChartDataSets[] = [];

    public chartOptions: ChartOptions = {
        responsive: true,
        plugins: {
            datalabels: {
                anchor: 'center',
                align: 'center',
                textAlign: 'center',
            },
        },
    };

    public months = months;

    public constructor() {
        this.years = getYears();
        this.selectedYear = new Date().getFullYear();
    }

    public ngOnChanges(): void {
        this.setBarCharData();
    }

    public setBarCharData(): void {
        if (!this.centers.length || !this.selectedYear) return;

        const selectedYear = +this.selectedYear;

        if (!this.selectedCenterId)
            this.selectedCenterId = `${this.centers[0].id}`;

        const center = this.centers.find(c => c.id === +this.selectedCenterId);

        if (!center) return;

        const dataEntries = this.dataEntries.filter(
            e => e.centerId === center.id
        );

        const last3Years: number[][] = [
            this.months.map(() => 0),
            this.months.map(() => 0),
            this.months.map(() => 0),
        ];

        dataEntries.forEach(e => {
            const diffYear = selectedYear - e.year;

            if (diffYear < 1 || diffYear > 3) return;

            last3Years[3 - diffYear][e.month - 1] += e.value;
        });

        const monthsAvg: number[] = last3Years[0].map((month, i) => {
            return (month + last3Years[1][i] + last3Years[2][i]) / 3;
        });

        const threeYearsMonthAvg: number =
            (last3Years[0].reduce((a, b) => a + b, 0) +
                last3Years[1].reduce((a, b) => a + b, 0) +
                last3Years[2].reduce((a, b) => a + b, 0)) /
            36;

        const arr = [...last3Years[0], ...last3Years[1], ...last3Years[2]].map(
            (v, i) => [i + 1, v]
        );

        const { m, b } = linearRegression(arr);

        const trends = this.months.map(month => m * (36 + month.value) + b);

        const monthsExpected = this.months.map((_, i) => {
            if (monthsAvg[i] <= 0 || threeYearsMonthAvg <= 0 || trends[i] <= 0)
                return 0;

            return Math.round((monthsAvg[i] / threeYearsMonthAvg) * trends[i]);
        });

        const monthsValues = this.months.map(m =>
            dataEntries
                .filter(e => e.year === selectedYear && e.month === m.value)
                .reduce((a, b) => a + b.value, 0)
        );

        this.monthsMistakePercentage = this.months.map((_, i) => {
            const value =
                monthsValues[i] === 0
                    ? -1
                    : monthsExpected[i] === 0
                    ? 1
                    : (monthsValues[i] - monthsExpected[i]) / monthsExpected[i];

            const colorValue = value + 0.5;
            const c = colorValue < 0 ? 0 : colorValue > 1 ? 1 : colorValue;

            return {
                value: `${value ? Math.round(value * 100) : 0}%`,
                color: getColor(c),
            };
        });

        this.chartLabels = this.months.map(m => m.label);
        this.chartData = [
            {
                data: monthsExpected,
                backgroundColor: expectedColor,
                label: 'عدد المتعاملين المتوقع',
            },
            {
                data: monthsValues,
                backgroundColor: currentColor,
                label: 'عدد المتعاملين الفعلي',
            },
        ];
    }
}
