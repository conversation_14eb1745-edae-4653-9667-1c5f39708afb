import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { isNotOrigin } from '@masar/common/utils';
import { OrganizationOrigin } from '@masar/common/enums';

@Component({
    templateUrl: './excel-sheet.component.html',
    styleUrls: ['./excel-sheet.component.scss'],
})
export class ExcelSheetComponent {
    public readonly password = '8915';

    public isLoggedIn = false;

    public constructor(router: Router) {
        const isNotAllowed = isNotOrigin([
            OrganizationOrigin.police,
            OrganizationOrigin.localhost,
        ]);

        if (isNotAllowed) router.navigate(['']).then();

        const savedPassword = localStorage.getItem('excel_password');
        if (
            savedPassword?.length === 12 &&
            savedPassword.substring(4, 8) === this.password
        ) {
            this.isLoggedIn = true;
        }
    }

    public login(password: string): void {
        if (password === this.password) {
            this.isLoggedIn = true;
            localStorage.setItem('excel_password', '12d2' + password + 'd123');
        }
    }
}
