<app-page
    pageTitle="{{
        (display === 'active'
            ? 'translate_active_kpis'
            : display === 'hidden'
            ? 'translate_hidden_kpis'
            : display === 'archived'
            ? 'translate_retired_kpis'
            : 'translate_deleted_kpis'
        ) | translate
    }}"
>
    <ng-container tools>
        <a
            *appHasPermissionId="permissionList.kpiWrite"
            [routerLink]="['', 'kpi', 'new']"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-plus me-2"></i>
            <span>{{ 'translate_add_new' | translate }}</span>
        </a>

        <button
            *appIsOrigin="[
                organizationOrigin.localhost,
                organizationOrigin.staging,
                organizationOrigin.amiriGuard,
                organizationOrigin.amiriGuard2
            ]"
            [appTooltip]="'translate_training_videos' | translate"
            class="btn btn-sm btn-outline-white"
            (click)="showTrainingVideosDialog()"
        >
            <i class="fa-solid fa-question"></i>
        </button>
    </ng-container>

    <app-kpi-list-full
        content
        [isFilterInUrl]="true"
        [display]="display"
    ></app-kpi-list-full>
</app-page>

<ng-template #controlsField let-item="item">
    <div class="flex flex-row items-center justify-center gap-2">
        <app-dropdown>
            <!-- Results -->
            <button
                *ngIf="display === 'active'"
                [disabled]="currentlyProcessing.has(item.id)"
                (click)="
                    showKpiResultsModal(
                        item.id,
                        '(' +
                            kpiPrefix +
                            '-' +
                            item.type.code +
                            '-' +
                            item.code +
                            ') ' +
                            item.name
                    )
                "
                class="btn btn-sm btn-success"
                [appTooltip]="'translate_results' | translate"
            >
                <i class="fa-light fa-chart-gantt fa-fw"></i>
            </button>

            <ng-container *appHasPermissionId="permissionList.kpiWrite">
                <!-- Edit -->
                <button
                    *ngIf="display === 'active'"
                    [disabled]="currentlyProcessing.has(item.id)"
                    (click)="navigateToEdit(item.id)"
                    class="btn btn-sm btn-info"
                    [appTooltip]="'translate_edit_kpi' | translate"
                >
                    <i class="fa-light fa-edit fa-fw"></i>
                </button>

                <!-- Hide -->
                <button
                    *ngIf="display === 'active'"
                    [disabled]="currentlyProcessing.has(item.id)"
                    (click)="hide(item)"
                    class="btn btn-sm btn-warning"
                    [appTooltip]="'translate_hide_kpi' | translate"
                >
                    <i class="fa-light fa-eye-slash fa-fw"></i>
                </button>

                <!-- Archive -->
                <button
                    *ngIf="display === 'active'"
                    [disabled]="currentlyProcessing.has(item.id)"
                    (click)="archive(item)"
                    class="btn btn-sm btn-primary"
                    [appTooltip]="'translate_retire_kpi' | translate"
                >
                    <i class="fa-light fa-archive fa-fw"></i>
                </button>

                <!-- Unhide -->
                <button
                    *ngIf="display === 'hidden'"
                    [disabled]="currentlyProcessing.has(item.id)"
                    (click)="activate(item, false)"
                    class="btn btn-sm btn-primary"
                    [appTooltip]="'translate_unhide_kpi' | translate"
                >
                    <i class="fa-light fa-eye fa-fw"></i>
                </button>

                <!-- Unarchive -->

                <button
                    *ngIf="display === 'archived'"
                    [disabled]="currentlyProcessing.has(item.id)"
                    (click)="activate(item, false)"
                    class="btn btn-sm btn-info"
                    [appTooltip]="'translate_unretire_kpi' | translate"
                >
                    <i class="fa-light fa-folder-open fa-fw"></i>
                </button>

                <!-- Recycle -->
                <button
                    *ngIf="display === 'removed'"
                    [disabled]="currentlyProcessing.has(item.id)"
                    (confirm)="activate(item, true, true)"
                    (deny)="activate(item, false, true)"
                    [swal]="{
                        title:
                            'translate_do_you_want_to_also_remove_all_results_attached_to_this_kpi_question_mark'
                            | translate,
                        confirmButtonText: 'translate_yes' | translate,
                        denyButtonText: 'translate_no' | translate,
                        cancelButtonText: 'translate_cancel' | translate,
                        showCancelButton: true,
                        showCloseButton: true,
                        showDenyButton: true
                    }"
                    class="btn btn-sm btn-primary"
                    [appTooltip]="'translate_recycle_kpi' | translate"
                >
                    <i class="fa-light fa-recycle fa-fw"></i>
                </button>
            </ng-container>

            <!-- Delete -->
            <ng-container *appHasPermissionId="permissionList.kpiDelete">
                <button
                    *ngIf="display === 'active'"
                    [disabled]="currentlyProcessing.has(item.id)"
                    (confirm)="remove(item)"
                    [swal]="{
                        title:
                            'translate_delete_this_item_question_mark'
                            | translate,
                        confirmButtonText: 'translate_yes' | translate,
                        cancelButtonText: 'translate_cancel' | translate,
                        showCancelButton: true,
                        showCloseButton: true
                    }"
                    class="btn btn-sm btn-danger"
                    [appTooltip]="'translate_delete_kpi' | translate"
                >
                    <i class="fas fa-trash fa-fw"></i>
                </button>
            </ng-container>
        </app-dropdown>
    </div>
</ng-template>
