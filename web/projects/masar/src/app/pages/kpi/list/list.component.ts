import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    NgModuleRef,
    OnDestroy,
    TemplateRef,
    ViewChild,
} from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, takeUntil } from 'rxjs/operators';
import { KpiListFullComponent } from '@masar/features/kpi-shared/components/kpi-list-full/kpi-list-full.component';
import { Kpi, KpiStatus } from '@masar/common/models';
import { ModalService, NotificationService } from 'mnm-webapp';
import { KpiService } from '../kpi.service';
import { permissionList } from '@masar/common/constants';
import { AppSettingFetcherService } from '@masar/core/services';
import { KpiResultsAndDiagramComponent } from './components/kpi-results-and-diagram/kpi-results-and-diagram.component';
import { IframeDialogComponent } from '@masar/features/dialogs/iframe-dialog/iframe-dialog.component';
import { TranslateService } from '@ngx-translate/core';
import { OrganizationOrigin } from '@masar/common/enums';

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
})
export class ListComponent implements AfterViewInit, OnDestroy {
    @ViewChild(KpiListFullComponent) private list: KpiListFullComponent;

    @ViewChild('controlsField') private controlFields: TemplateRef<any>;

    public currentlyProcessing = new Set<string>();

    public display: KpiStatus;

    public permissionList = permissionList;

    public kpiPrefix: string = '';

    public readonly organizationOrigin = OrganizationOrigin;

    private unsubscribeAll = new Subject();

    public constructor(
        private notificationService: NotificationService,
        private router: Router,
        private modalService: ModalService,
        private activatedRoute: ActivatedRoute,
        private changeDetectorRef: ChangeDetectorRef,
        private kpiService: KpiService,
        private moduleRef: NgModuleRef<any>,
        private appSettingFetcherService: AppSettingFetcherService,
        private readonly translateService: TranslateService
    ) {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(({ appId }) => (this.kpiPrefix = appId));
    }

    public ngAfterViewInit(): void {
        this.activatedRoute.url
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(url => {
                this.display = (url[0]?.path as KpiStatus) || 'active';
                this.list.list.customFields = [
                    {
                        name: '',
                        template: this.controlFields,
                    },
                ];

                this.changeDetectorRef.detectChanges();
            });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public activate(
        item: Kpi,
        clean: boolean,
        redirectToEdit: boolean = false
    ): void {
        this.process(
            item,
            this.kpiService.activate(item.id, clean),
            redirectToEdit
                ? async () => {
                      await this.router.navigate(['', 'kpi', 'edit', item.id]);
                  }
                : () => this.list.tableController.filter$.next(false)
        );
    }

    public hide(item: Kpi): void {
        this.process(item, this.kpiService.hide(item.id), () =>
            this.list.tableController.filter$.next(false)
        );
    }

    public archive(item: Kpi): void {
        this.process(item, this.kpiService.archive(item.id), () =>
            this.list.tableController.filter$.next(false)
        );
    }

    public remove(item: Kpi): void {
        this.process(item, this.kpiService.remove(item.id), () =>
            this.list.tableController.filter$.next(false)
        );
    }

    public showKpiResultsModal(id: string, title: string): void {
        this.currentlyProcessing.add(id);

        this.kpiService.get(id).subscribe(async item => {
            await this.modalService.show(KpiResultsAndDiagramComponent, {
                title,
                beforeInit: c => {
                    c.kpi = item;
                },
                onDismiss: () => {
                    this.currentlyProcessing.delete(id);
                },
                size: {
                    width: '100%',
                    height: '100%',
                },
                moduleRef: this.moduleRef,
            });
        });
    }

    public async navigateToEdit(id: string): Promise<void> {
        await this.router.navigate(['', 'kpi', 'edit', id]);
    }

    public showTrainingVideosDialog(): void {
        this.modalService
            .show(IframeDialogComponent, {
                size: {
                    width: '80%',
                    height: '100%',
                },
                moduleRef: this.moduleRef,
                title: this.translateService.instant(
                    'translate_training_videos'
                ),
                beforeInit: c => {
                    c.title = this.translateService.instant(
                        'translate_video_add_new_kpi'
                    );
                    c.url = 'https://www.youtube.com/embed/-KDkLffGCRM';
                },
            })
            .then();
    }

    private process(
        item: Kpi,
        observable: Observable<string>,
        callback: () => void = () => {
            return undefined;
        }
    ): void {
        this.currentlyProcessing.add(item.id);
        observable
            .pipe(
                finalize(() => {
                    this.currentlyProcessing.delete(item.id);
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                callback();
            });
    }
}
