<app-page pageTitle="{{ 'translate_data_entry_request_details' | translate }}">
    <!-- Tools -->
    <ng-container tools>
        <!-- List -->
        <a
            class="btn btn-sm btn-success"
            [routerLink]="['', 'kpi', 'data-entry-request']"
        >
            <i class="fa-light fa-share"></i>
            <span class="hidden md:inline">
                {{ 'translate_data_entry_requests' | translate }}
            </span>
        </a>

        <!-- New -->
        <a
            class="btn btn-sm btn-primary"
            [routerLink]="['', 'kpi', 'data-entry-request', 'new']"
        >
            <i class="fa-light fa-plus"></i>
            <span class="hidden md:inline">
                {{ 'translate_add_new' | translate }}
            </span>
        </a>

        <!-- Edit -->
        <a
            class="btn btn-sm btn-info"
            *ngIf="kpiResultRequest"
            [routerLink]="[
                '',
                'kpi',
                'data-entry-request',
                'edit',
                kpiResultRequest.id
            ]"
            [appTooltip]="'translate_edit' | translate"
        >
            <i class="fa-light fa-edit"></i>
            <span class="hidden md:inline">
                {{ 'translate_edit' | translate }}
            </span>
        </a>
    </ng-container>

    <div content class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <!-- Details -->
        <app-content [contentTitle]="'translate_details' | translate">
            <table content>
                <tbody>
                    <!-- Kpi Request Year -->
                    <tr>
                        <td>{{ 'translate_year' | translate }}</td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="kpiResultRequest"
                            >
                                {{ kpiResultRequest.year }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Kpi Request Start Time -->
                    <tr>
                        <td>{{ 'translate_start_time' | translate }}</td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="kpiResultRequest"
                            >
                                {{
                                    kpiResultRequest.startTime
                                        | date : 'yyyy-MM-dd'
                                }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Kpi Request End Time -->
                    <tr>
                        <td>{{ 'translate_end_time' | translate }}</td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="kpiResultRequest"
                            >
                                {{
                                    kpiResultRequest.endTime
                                        | date : 'yyyy-MM-dd'
                                }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Kpi Request Measurement Cycle -->
                    <tr>
                        <td>{{ 'translate_measurement_cycle' | translate }}</td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="kpiResultRequest"
                            >
                                {{
                                    kpiResultRequest.measurementCycle
                                        | translateItem : 'kpi-cycle'
                                        | async
                                }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Kpi Request Periods -->
                    <tr>
                        <td>{{ 'translate_periods' | translate }}</td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="kpiResultRequest"
                            >
                                <span
                                    *ngFor="
                                        let period of kpiResultRequest.periods;
                                        let idx = index
                                    "
                                >
                                    {{ period + 1 }}
                                    <span
                                        *ngIf="
                                            kpiResultRequest.periods.length !==
                                            idx + 1
                                        "
                                        >,
                                    </span>
                                </span>
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Kpi Request Path -->
                    <tr>
                        <td>{{ 'translate_path' | translate }}</td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="kpiResultRequest"
                            >
                                {{
                                    kpiResultRequest.path
                                        | translateItem
                                            : 'kpi-result-data-entry-request-path'
                                        | async
                                }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Kpi Request IsSpecial -->
                    <tr>
                        <td>{{ 'translate_only_special_kpis' | translate }}</td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="kpiResultRequest"
                            >
                                {{
                                    (kpiResultRequest.isSpecial
                                        ? 'translate_yes'
                                        : 'translate_no'
                                    ) | translate
                                }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Kpi Request Note -->
                    <tr>
                        <td>{{ 'translate_notes' | translate }}</td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="kpiResultRequest"
                            >
                                {{ kpiResultRequest.note }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Kpi Created By -->
                    <tr>
                        <td>{{ 'translate_created_by' | translate }}</td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="kpiResultRequest"
                            >
                                {{ kpiResultRequest.createdBy.name }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Kpi Created At -->
                    <tr>
                        <td>{{ 'translate_created_at' | translate }}</td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="kpiResultRequest"
                            >
                                {{
                                    kpiResultRequest.createdTime
                                        | date : 'yyyy-MM-dd'
                                }}<br />{{
                                    kpiResultRequest.createdTime
                                        | date : 'hh:mm a'
                                }}
                            </ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>

        <!-- Linked Departments & Kpis -->
        <div>
            <div class="grid gap-3">
                <!-- Completed responses ratio -->
                <app-content
                    [contentTitle]="
                        'translate_completed_responses_ratio' | translate
                    "
                >
                    <app-content-loading
                        content
                        [isLoading]="!kpiResultRequest"
                    >
                        <div class="text-center">
                            <app-progress-ring
                                [value]="kpiResultRequest?.completionRate"
                                [radius]="80"
                                [strokeWidth]="6"
                            ></app-progress-ring>
                        </div>
                    </app-content-loading>
                </app-content>

                <!-- Linked Departments -->
                <app-content
                    [contentTitle]="'translate_departments' | translate"
                >
                    <app-content-loading
                        content
                        [isLoading]="!kpiResultRequest"
                    >
                        <div class="max-h-[200px] overflow-auto">
                            <table>
                                <tbody>
                                    <tr
                                        *ngFor="
                                            let item of kpiResultRequest?.departments
                                        "
                                    >
                                        <td>
                                            <a
                                                *appHasPermissionId="
                                                    permissionList.departmentRead;
                                                    else justNameTemplateRef
                                                "
                                                [routerLink]="[
                                                    '',
                                                    'department',
                                                    'detail',
                                                    item.id
                                                ]"
                                            >
                                                {{ item.name }}
                                            </a>
                                            <ng-template #justNameTemplateRef>
                                                {{ item.name }}
                                            </ng-template>
                                        </td>
                                    </tr>
                                    <tr
                                        class="italic"
                                        *ngIf="
                                            kpiResultRequest?.departments
                                                ?.length === 0
                                        "
                                    >
                                        <td>
                                            {{
                                                'translate_all_departments'
                                                    | translate
                                            }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </app-content-loading>
                </app-content>

                <!-- Linked Kpis -->
                <app-content [contentTitle]="'translate_kpis' | translate">
                    <app-content-loading
                        content
                        [isLoading]="!kpiResultRequest"
                    >
                        <div class="max-h-[200px] overflow-auto">
                            <table class="table-bordered table-striped table">
                                <tbody>
                                    <tr
                                        *ngFor="
                                            let item of kpiResultRequest?.kpis
                                        "
                                    >
                                        <td>
                                            <a
                                                *appHasPermissionId="
                                                    permissionList.kpiRead;
                                                    else justNameTemplateRef
                                                "
                                                [routerLink]="[
                                                    '',
                                                    'kpi',
                                                    'detail',
                                                    item.id
                                                ]"
                                            >
                                                {{ item.name }}
                                            </a>
                                            <ng-template #justNameTemplateRef>
                                                {{ item.name }}
                                            </ng-template>
                                        </td>
                                    </tr>
                                    <tr
                                        class="italic"
                                        *ngIf="
                                            kpiResultRequest?.kpis?.length === 0
                                        "
                                    >
                                        <td>
                                            {{
                                                'translate_all_kpis' | translate
                                            }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </app-content-loading>
                </app-content>
            </div>
        </div>

        <!-- Responses Table -->
        <app-content
            class="col-span-2"
            contentTitle="{{ 'translate_responses' | translate }}"
        >
            <ng-container content>
                <app-responses-table
                    *ngIf="kpiResultRequest"
                    [tableController]="tableController"
                    [readOnly]="true"
                ></app-responses-table>
            </ng-container>
        </app-content>
    </div>
</app-page>
