import { MnmFormField } from '@masar/shared/components';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },
    {
        fields: [
            {
                name: 'users',
                type: 'select',
                label: 'translate_move_the_request_to_the_next_level',
                multiple: true,
                bindLabel: 'name',
                size: 6,
                isDisabled: true,
            },
            {
                name: 'notes',
                type: 'textarea',
                label: 'translate_notes',
                size: 6,
                isDisabled: true,
            },
        ],
    },
];
