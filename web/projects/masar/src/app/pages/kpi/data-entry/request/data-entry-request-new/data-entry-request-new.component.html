<app-page
    pageTitle="{{
        (mode === 'new'
            ? 'translate_add_new_request'
            : 'translate_update_existing_request'
        ) | translate
    }}"
>
    <!-- Tools -->
    <a
        tools
        [routerLink]="['', 'kpi', 'data-entry-request']"
        class="btn btn-sm btn-outline-white"
    >
        <i class="fa-light fa-share me-2"></i>
        <span>{{ 'translate_back' | translate }}</span>
    </a>

    <!-- Form -->
    <mnm-form
        content
        *ngIf="formState"
        [state]="formState"
        [translateLabels]="true"
    >
        <div class="form-group form-row justify-content-center mt-2">
            <button
                type="submit"
                class="btn btn-primary"
                (click)="submit()"
                [disabled]="isSubmitting"
            >
                <app-loading-ring
                    *ngIf="isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i class="fa-light fa-save me-2"></i>
                <span>{{ 'translate_save' | translate }}</span>
            </button>
        </div>
    </mnm-form>
</app-page>
