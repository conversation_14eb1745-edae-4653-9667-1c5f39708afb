import { Component, NgZ<PERSON>, OnD<PERSON>roy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { TableController } from '@masar/common/misc/table';
import { KpiResultDataEntryResponse, Item } from '@masar/common/models';
import { MiscApiService } from '@masar/core/services';
import { KpiResultDataEntryResponseService } from '../../../kpi-result-data-entry-response.service';
import { functions } from '@masar/common/misc/functions';

@Component({
    selector: 'app-data-entry-response-list',
    templateUrl: './data-entry-response-list.component.html',
})
export class DataEntryResponseListComponent implements OnDestroy {
    public tableController: TableController<
        KpiResultDataEntryResponse,
        { keyword?: string; departmentIds?: string[]; assignees?: string[] }
    >;

    public departments: Item[] = [];
    public statuses: Item[];

    public readonly currentlyProcessing = new Set<string>();

    public constructor(
        private kpiResultDataEntryResponseService: KpiResultDataEntryResponseService,
        private miscApiService: MiscApiService,
        private ngZone: NgZone,
        translateService: TranslateService
    ) {
        this.loadItems();

        this.statuses = ['all', 'running', 'done'].map(x => {
            return { id: x, name: translateService.instant(`translate_${x}`) };
        });

        this.tableController = new TableController<
            KpiResultDataEntryResponse,
            { keyword?: string; departmentIds?: string[]; assignees?: string[] }
        >(
            filter =>
                this.kpiResultDataEntryResponseService.list(
                    filter.data.keyword,
                    filter.data.departmentIds,
                    filter.data.assignees,
                    filter.pageNumber,
                    filter.pageSize
                ),
            { data: { keyword: '', departmentIds: [], assignees: [] } }
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public export(): void {
        this.currentlyProcessing.add('export');

        this.kpiResultDataEntryResponseService
            .export(
                this.tableController.filter.data.keyword,
                this.tableController.filter.data.departmentIds,
                this.tableController.filter.data.assignees
            )
            .subscribe(file => {
                this.ngZone.runOutsideAngular(() => {
                    functions.downloadBlobIntoFile(file);
                });

                this.currentlyProcessing.delete('export');
            });
    }

    private loadItems(): void {
        this.miscApiService
            .departments()
            .subscribe(items => (this.departments = items));
    }
}
