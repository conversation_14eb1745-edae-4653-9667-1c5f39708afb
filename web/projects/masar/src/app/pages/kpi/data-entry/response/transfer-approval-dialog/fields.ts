import { MnmFormField } from '@masar/shared/components';
import { Validators } from '@angular/forms';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },
    {
        fields: [
            {
                name: 'users',
                type: 'select',
                label: 'translate_move_to',
                multiple: true,
                bindLabel: 'name',
                size: 6,
                validators: [Validators.required],
            },
            {
                name: 'notes',
                type: 'textarea',
                label: 'translate_notes',
                size: 6,
            },
        ],
    },
];
