import { Component, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { permissionList } from '@masar/common/constants';
import { TableController } from '@masar/common/misc/table';
import {
    KpiResultDataEntryRequest,
    KpiResultDataEntryResponse,
} from '@masar/common/models';
import { first } from 'rxjs/operators';
import { KpiResultDataEntryRequestService } from '../../../kpi-result-data-entry-request.service';

@Component({
    selector: 'app-data-entry-request-detail',
    templateUrl: './data-entry-request-detail.component.html',
})
export class DataEntryRequestDetailComponent implements OnDestroy {
    public kpiResultRequest: KpiResultDataEntryRequest;
    public kpiResultRequestId: string;

    public permissionList = permissionList;

    public tableController: TableController<
        KpiResultDataEntryResponse,
        { keyword?: string; departmentIds?: string[]; status?: string }
    >;

    public constructor(
        private kpiResultDataEntryRequestService: KpiResultDataEntryRequestService,
        private activatedRoute: ActivatedRoute
    ) {
        this.activatedRoute.params.pipe(first()).subscribe(params => {
            {
                this.kpiResultRequestId = params.id;
                this.kpiResultDataEntryRequestService
                    .get(this.kpiResultRequestId)
                    .subscribe(item => {
                        this.kpiResultRequest = item;
                        this.getListResponses();
                    });
            }
        });
    }

    public getListResponses(): void {
        this.tableController = new TableController<
            KpiResultDataEntryResponse,
            { keyword?: string; departmentIds?: string[]; assignees?: string[] }
        >(
            filter =>
                this.kpiResultDataEntryRequestService.listResponses(
                    this.kpiResultRequestId,
                    filter.data.keyword,
                    filter.data.departmentIds,
                    filter.data.assignees,
                    filter.pageNumber,
                    filter.pageSize
                ),
            { data: { keyword: '', departmentIds: [], assignees: [] } }
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }
}
