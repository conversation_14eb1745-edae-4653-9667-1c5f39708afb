import { Component, Input, NgModuleRef, On<PERSON><PERSON>roy } from '@angular/core';
import { permissionList } from '@masar/common/constants';
import { TableController } from '@masar/common/misc/table';
import { Item, KpiResultDataEntryResponse } from '@masar/common/models';
import { MiscApiService } from '@masar/core/services';
import { ModalService } from 'mnm-webapp';
import { DataEntryResponseDetailComponent } from '../data-entry-response-detail/data-entry-response-detail.component';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TransferApprovalDialogComponent } from '@masar/pages/kpi/data-entry/response/transfer-approval-dialog/transfer-approval-dialog.component';
import { MiscItem } from '@masar/common/types';

type Field = 'year' | 'department' | 'kpi' | 'stage' | 'time';

type Filter = 'keyword' | 'departmentIds' | 'assignees';

@Component({
    selector: 'app-responses-table',
    templateUrl: './responses-table.component.html',
})
export class ResponsesTableComponent implements OnDestroy {
    @Input() public readOnly: boolean = true;

    @Input() public tableController: TableController<
        KpiResultDataEntryResponse,
        { keyword?: string; departmentIds?: string[]; assignees?: string[] }
    >;

    @Input() public shownFilters: Record<Filter, boolean> = {
        keyword: true,
        departmentIds: true,
        assignees: true,
    };

    @Input() public shownFields: Record<Field, boolean> = {
        year: true,
        department: true,
        kpi: true,
        stage: true,
        time: true,
    };

    @Input() public kpiId: string;

    public departments: Item[] = [];

    public assignees: Item[];

    public permissionList = permissionList;

    private readonly unsubscribeAll = new Subject();

    public constructor(
        private readonly miscApiService: MiscApiService,
        private readonly modalService: ModalService,
        private readonly moduleRef: NgModuleRef<any>
    ) {
        this.loadItems();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public async showResponseDetailDialog(id: string): Promise<void> {
        const subject = new Subject();
        const component = await this.modalService.show(
            DataEntryResponseDetailComponent,
            {
                beforeInit: c => {
                    c.kpiResultDataEntryResponseId = id;
                    c.readOnly = this.readOnly;
                },
                moduleRef: this.moduleRef,
                size: {
                    width: '100%',
                    height: '100%',
                },
                onDismiss: () => {
                    subject.next();
                    subject.complete();
                },
            }
        );

        component.transferMove.pipe(takeUntil(subject)).subscribe(async () => {
            await this.modalService.dismiss(component);
            this.tableController.filter$.next();
        });
    }

    public async showTransferApprovalDialog(
        id: string,
        users: MiscItem[]
    ): Promise<void> {
        const subject = new Subject();
        const component = await this.modalService.show(
            TransferApprovalDialogComponent,
            {
                beforeInit: c => {
                    c.kpiResultDataEntryResponseId = id;
                    c.currentApprovers = users;
                },
                moduleRef: this.moduleRef,
                size: {
                    width: '75%',
                    height: '75%',
                },
                onDismiss: () => {
                    subject.next();
                    subject.complete();
                },
            }
        );

        component.transferMove
            .pipe(takeUntil(subject))
            .subscribe(async updateTable => {
                await this.modalService.dismiss(component);
                if (updateTable) {
                    this.tableController.filter$.next();
                }
            });
    }

    private loadItems(): void {
        this.miscApiService
            .departments()
            .subscribe(items => (this.departments = items));

        this.miscApiService
            .getList('kpi-result-data-entry-response-transfer-assignee')
            .subscribe(items => (this.assignees = items));
    }
}
