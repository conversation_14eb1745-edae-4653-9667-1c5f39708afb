import { Validators } from '@angular/forms';
import { MnmFormField } from '@masar/shared/components';
import { isOrigin } from '@masar/common/utils';
import { OrganizationOrigin } from '@masar/common/enums';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },
    {
        fields: [
            {
                name: 'year',
                type: 'select',
                label: 'translate_year',
                size: 4,
                notClearable: true,
                notSearchable: true,
                validators: [Validators.required],
            },
            {
                name: 'measurementCycle',
                type: 'select',
                label: 'translate_measurement_cycle',
                size: 4,
                bindLabel: 'name',
                bindValue: 'id',
                compareWith: (a, b) => a.id === b,
                notClearable: true,
                notSearchable: true,
                validators: [Validators.required],
            },
            {
                name: 'periods',
                type: 'select',
                label: 'translate_periods',
                multiple: true,
                size: 4,
                bindValue: 'id',
                bindLabel: 'name',
                compareWith: (a, b) => a.id == b,
                validators: [Validators.required],
            },
        ],
    },

    {
        fields: [
            {
                name: 'path',
                type: 'select',
                label: 'translate_path',
                size: 4,
                bindLabel: 'name',
                bindValue: 'id',
                compareWith: (a, b) => a.id === b,
                notClearable: true,
                notSearchable: true,
                validators: [Validators.required],
            },
            {
                name: 'startTime',
                type: 'date',
                label: 'translate_start_time',
                size: 4,
                validators: [Validators.required],
            },

            {
                name: 'endTime',
                type: 'date',
                label: 'translate_end_time',
                size: 4,
                validators: [Validators.required],
            },
        ],
    },
    {
        name: 'KpiTypes',
        type: 'select',
        label: 'translate_kpi_type',
        bindLabel: 'name',
        multiple: true,
        hide: isOrigin([OrganizationOrigin.injaz, OrganizationOrigin.police]),
    },
    {
        name: 'note',
        type: 'textarea',
        label: 'translate_notes',
    },
    {
        fields: [
            {
                name: 'isSpecial',
                type: 'checkbox',
                label: 'translate_only_special_kpis',
                defaultValue: false,
            },
            {
                name: 'shouldExcludeResultsWithValues',
                type: 'checkbox',
                label: 'translate_should_exclude_results_with_values',
                defaultValue: false,
            },
        ],
    },
    {
        fields: [
            {
                name: 'departments',
                type: 'select',
                label: 'translate_departments',
                multiple: true,
                size: 6,
                bindLabel: 'name',
            },

            {
                name: 'kpis',
                type: 'select',
                label: 'translate_kpis',
                multiple: true,
                size: 6,
                bindLabel: 'name',
            },
        ],
    },
];
