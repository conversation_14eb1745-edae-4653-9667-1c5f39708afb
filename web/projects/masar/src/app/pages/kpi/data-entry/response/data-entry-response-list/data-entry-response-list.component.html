<app-page pageTitle="{{ 'translate_data_entry_responses' | translate }}">
    <ng-container tools>
        <!-- Export -->
        <button
            (click)="export()"
            [disabled]="currentlyProcessing.has('export')"
            [appTooltip]="'translate_export' | translate"
            class="btn btn-sm btn-outline-white"
        >
            <span>{{ 'translate_export_report' | translate }}</span>
            <i class="fa-light fa-file-export ms-2"></i>
        </button>
    </ng-container>

    <!-- Content -->
    <div content>
        <app-responses-table
            *ngIf="tableController"
            [tableController]="tableController"
            [readOnly]="false"
        ></app-responses-table>
    </div>
</app-page>
