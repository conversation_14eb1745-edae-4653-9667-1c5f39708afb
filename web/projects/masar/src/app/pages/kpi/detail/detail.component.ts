import { Component, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import {
    Capability,
    Item,
    Kpi,
    KpiBenchmark,
    KpiResultDataEntryResponse,
} from '@masar/common/models';
import { KpiService } from '../kpi.service';
import { ActivatedRoute } from '@angular/router';
import { first, takeUntil } from 'rxjs/operators';
import {
    AppSettingFetcherService,
    MiscApiService,
    PermissionService,
} from '@masar/core/services';
import { permissionList } from '@masar/common/constants';
import { ModalService } from 'mnm-webapp';
import { Observable, Subject } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { TableController, TableResult } from '@masar/common/misc/table';
import { NewUserRequestDialogComponent } from '@masar/features/masar/components';
import { KpiBenchmarkNewComponent } from '@masar/features/kpi-shared/components/kpi-benchmark-new/kpi-benchmark-new.component';
import { KpiBenchmarkListComponent } from '@masar/features/kpi-shared/components/kpi-benchmark-list/kpi-benchmark-list.component';
import { OrganizationOrigin } from '@masar/common/enums';
import { KpiResultDataEntryResponseService } from '../kpi-result-data-entry-response.service';

interface ResponsesTableFilters {
    keyword?: string;
    departmentIds?: string[];
    assignees?: string[];
}

@Component({
    templateUrl: './detail.component.html',
})
export class DetailComponent implements OnDestroy {
    @ViewChild('benchmarkList')
    private benchmarkList: KpiBenchmarkListComponent;

    public readonly organizationOrigin = OrganizationOrigin;

    public responsesTableController: TableController<
        KpiResultDataEntryResponse,
        ResponsesTableFilters
    >;

    public kpi: Kpi;
    public kpiId: string;
    public kpiPrefix: string = '';

    public cycles: Item[];
    public directions: Item[];
    public units: Item[];
    public dataEntryMethods: Item[];
    public targetSettingMethods: Item[];
    public permissionList = permissionList;
    public currentYear = new Date().getFullYear();
    public optionalFields: Record<string, boolean> = {};

    public aiAnalysisObservable: Observable<string>;

    private unsubscribeAll = new Subject();

    public constructor(
        private kpiService: KpiService,
        private activatedRoute: ActivatedRoute,
        private modalService: ModalService,
        private moduleRef: NgModuleRef<any>,
        private translateService: TranslateService,
        private kpiResultDataEntryResponseService: KpiResultDataEntryResponseService,
        private readonly permissionService: PermissionService,
        private kpiSettingService: AppSettingFetcherService,
        miscApiService: MiscApiService
    ) {
        miscApiService
            .getList('kpi-cycle')
            .subscribe(items => (this.cycles = items));

        miscApiService
            .getList('kpi-direction')
            .subscribe(items => (this.directions = items));

        miscApiService
            .getList('kpi-unit')
            .subscribe(items => (this.units = items));

        miscApiService
            .getList('kpi-data-entry-method')
            .subscribe(items => (this.dataEntryMethods = items));

        miscApiService
            .getList('kpi-result-target-setting-method', undefined, true)
            .subscribe(items => (this.targetSettingMethods = items));

        kpiSettingService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(({ appId }) => (this.kpiPrefix = appId));

        this.activatedRoute.params.pipe(first()).subscribe(params => {
            this.kpiId = params['id'];
            this.aiAnalysisObservable = this.kpiService.aiAnalyze(this.kpiId);
            this.refresh();
        });
        this.syncKpiSettings();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public refresh(): void {
        this.kpiService.get(this.kpiId).subscribe(item => {
            this.kpi = item;
        });

        this.responsesTableController = new TableController<
            KpiResultDataEntryResponse,
            ResponsesTableFilters
        >(
            filter =>
                this.kpiResultDataEntryResponseService.list(
                    filter.data.keyword,
                    filter.data.departmentIds,
                    filter.data.assignees,
                    filter.pageNumber,
                    filter.pageSize,
                    this.kpiId
                ),
            { data: { keyword: '', departmentIds: [], assignees: [] } }
        );

        this.permissionService
            .ensureUserHasPermission(permissionList.kpi)
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(hasPermission => {
                if (hasPermission) this.responsesTableController.start();
            });
    }

    public async showCreateBenchmarkDialog(
        benchmark: KpiBenchmark = null
    ): Promise<void> {
        await KpiBenchmarkNewComponent.showAsDialog(
            benchmark,
            this.kpiId,
            this.modalService,
            this.translateService,
            this.moduleRef,
            x => this.benchmarkList.addNewBenchmark(x),
            x => this.benchmarkList.updateExistingBenchmark(x)
        );
    }

    // Other
    public getNameForId(id: string, array: Item[]): string {
        return array?.find(x => x.id === id)?.name || id;
    }

    // Capability linking methods
    public listLinkedCapabilities(): (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Capability>> {
        return (keyword: string, pageNumber: number, pageSize: number) =>
            this.kpiService.listLinkedCapabilities(
                this.kpiId,
                keyword,
                pageNumber,
                pageSize
            );
    }

    // Capability linking methods
    public listUnlinkedCapabilities(): (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Capability>> {
        return (keyword: string, pageNumber: number, pageSize: number) =>
            this.kpiService.listUnlinkedCapabilities(
                this.kpiId,
                keyword,
                pageNumber,
                pageSize
            );
    }

    public linkCapability(): (capabilityId: string) => Observable<string> {
        return (capabilityId: string) =>
            this.kpiService.linkCapability(this.kpiId, capabilityId);
    }

    public unlinkCapability(): (capabilityId: string) => Observable<string> {
        return (capabilityId: string) =>
            this.kpiService.unlinkCapability(this.kpiId, capabilityId);
    }

    public showRequestDialog(): void {
        this.translateService
            .get('translate_new_request')
            .subscribe(async str => {
                const subject = new Subject();
                const component = await this.modalService.show(
                    NewUserRequestDialogComponent,
                    {
                        title: str,
                        moduleRef: this.moduleRef,
                        onDismiss: () => {
                            subject.next();
                            subject.complete();
                        },
                        beforeInit: c => {
                            c.mode = 'new_request';
                            c.itemId = this.kpiId;
                            c.type = 'kpi';
                        },
                    }
                );
                component.requestCreated
                    .pipe(takeUntil(subject))
                    .subscribe(_ => {
                        this.modalService.dismiss(component);
                    });
            });
    }

    private syncKpiSettings(): void {
        this.kpiSettingService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(({ kpiSetting }) => {
                const isFieldEnabled: (name: string) => boolean = (
                    name: string
                ) =>
                    kpiSetting.optionalFields.find(x => x.name === name)
                        .isEnabled;

                const fields = ['measuring_department'];
                fields.forEach(
                    field =>
                        (this.optionalFields[field] = isFieldEnabled(field))
                );
            });
    }
}
