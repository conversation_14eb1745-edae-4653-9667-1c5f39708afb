<app-page pageTitle="{{ 'translate_details' | translate }}">
    <ng-container tools>
        <!-- Ai analyze -->
        <app-ai-analyze-button
            *appHasPermissionId="permissionList.kpiAi"
            [analysisObservable]="aiAnalysisObservable"
        ></app-ai-analyze-button>

        <!-- Add new request -->
        <button
            (click)="showRequestDialog()"
            class="btn btn-sm btn-outline-white"
        >
            <i class="far fa-comment"></i>
            {{ 'translate_new_request' | translate }}
        </button>

        <!-- Kpi benchmarks -->
        <button
            *appHasPermissionId="permissionList.kpi"
            class="btn btn-sm btn-outline-white"
            (click)="showCreateBenchmarkDialog()"
        >
            <i class="fa-light fa-compress-arrows-alt"></i>
            <span class="hidden md:inline">
                {{ 'translate_new_benchmark' | translate }}</span
            >
        </button>

        <!-- New -->
        <a
            *appHasPermissionId="permissionList.kpiWrite"
            class="btn btn-sm btn-primary"
            [routerLink]="['', 'kpi', 'new']"
        >
            <i class="fa-light fa-plus"></i>
            <span class="hidden md:inline">
                {{ 'translate_add_new' | translate }}
            </span>
        </a>

        <!-- Edit -->
        <a
            *appHasPermissionId="permissionList.kpiWrite"
            class="btn btn-sm btn-info"
            [routerLink]="['', 'kpi', 'edit', kpiId]"
            [appTooltip]="'translate_edit' | translate"
        >
            <i class="fa-light fa-edit"></i>
            <span class="hidden md:inline">
                {{ 'translate_edit' | translate }}
            </span>
        </a>

        <!-- Back -->
        <a
            class="btn btn-sm btn-white flex items-center gap-1"
            [routerLink]="['', 'kpi']"
        >
            <i class="fas fa-left"></i>
            <span class="hidden md:inline">
                {{ 'translate_back' | translate }}
            </span>
        </a>
    </ng-container>

    <div class="grid grid-cols-1 gap-4" content>
        <app-alert
            *ngIf="kpi?.results?.length === 0"
            [mode]="'warning'"
            [description]="'translate_no_results_placeholder' | translate"
        >
            <ng-container
                *appHasPermissionId="permissionList.kpiResultWrite"
                tools
            >
                <a
                    *ngIf="kpi"
                    class="btn btn-sm btn-primary"
                    [routerLink]="['', 'kpi', 'update-result', kpi?.id]"
                    [queryParams]="{
                        year: currentYear,
                        departmentId: kpi.owningDepartment.id
                    }"
                >
                    <i class="fa-light fa-plus"></i>
                    <span> {{ 'translate_add_new_year' | translate }}</span>
                </a>
            </ng-container>
        </app-alert>

        <!-- DETAILS -->
        <app-content [contentTitle]="'translate_kpi_card' | translate">
            <ng-container content>
                <div
                    class="mb-3 flex flex-wrap items-center justify-between gap-2 text-center"
                    *ngIf="kpi"
                >
                    <!-- Code -->
                    <ng-container
                        [ngTemplateOutlet]="badgeTemplate"
                        [ngTemplateOutletContext]="{
                            value:
                                kpiPrefix +
                                '-' +
                                kpi.type.code +
                                '-' +
                                kpi.code,
                            title: 'translate_kpi_code'
                        }"
                    ></ng-container>

                    <!-- Type -->
                    <ng-container
                        [ngTemplateOutlet]="badgeTemplate"
                        [ngTemplateOutletContext]="{
                            value: kpi.type.name,
                            title: 'translate_type'
                        }"
                    ></ng-container>

                    <!-- Cycle -->
                    <ng-container
                        [ngTemplateOutlet]="badgeTemplate"
                        [ngTemplateOutletContext]="{
                            value: getNameForId(kpi.measurementCycle, cycles),
                            title: 'translate_measurement_cycle'
                        }"
                    ></ng-container>

                    <!-- Direction -->
                    <ng-container
                        [ngTemplateOutlet]="badgeTemplate"
                        [ngTemplateOutletContext]="{
                            value: getNameForId(kpi.direction, directions),
                            title: 'translate_direction'
                        }"
                    ></ng-container>

                    <!-- Year -->
                    <ng-container
                        [ngTemplateOutlet]="badgeTemplate"
                        [ngTemplateOutletContext]="{
                            value: kpi?.creationYear,
                            title: 'translate_creation_year'
                        }"
                    ></ng-container>

                    <!-- Formula -->
                    <ng-container
                        [ngTemplateOutlet]="badgeTemplate"
                        [ngTemplateOutletContext]="{
                            value: kpi?.formula,
                            title: 'translate_formula'
                        }"
                    ></ng-container>

                    <!-- Units -->
                    <ng-container
                        [ngTemplateOutlet]="badgeTemplate"
                        [ngTemplateOutletContext]="{
                            value: getNameForId(kpi?.units, units),
                            title: 'translate_measuring_unit'
                        }"
                    ></ng-container>

                    <!-- Data entry method -->
                    <ng-container
                        [ngTemplateOutlet]="badgeTemplate"
                        [ngTemplateOutletContext]="{
                            value: getNameForId(
                                kpi?.dataEntryMethod,
                                dataEntryMethods
                            ),
                            title: 'translate_data_entry_method'
                        }"
                    ></ng-container>
                </div>

                <table class="table-bordered table-striped table">
                    <tbody>
                        <!-- Name -->
                        <tr style="font-size: 1.2rem">
                            <td>{{ 'translate_kpi' | translate }}</td>
                            <td>
                                <strong>
                                    <app-value-loading
                                        [isLoading]="!kpi"
                                        [value]="kpi?.name"
                                    ></app-value-loading>
                                </strong>
                            </td>
                        </tr>

                        <!-- Description-->
                        <tr>
                            <td>
                                {{ 'translate_description' | translate }}
                            </td>
                            <td class="whitespace-pre-line">
                                <app-value-loading
                                    [isLoading]="!kpi"
                                    [value]="kpi?.description"
                                ></app-value-loading>
                            </td>
                        </tr>
                        <!--Measurement Department-->
                        <tr *ngIf="optionalFields['measuring_department']">
                            <td>
                                {{
                                    'translate_kpi_measuring_department'
                                        | translate
                                }}
                            </td>
                            <td class="whitespace-pre-line">
                                <a
                                    *appHasPermissionId="
                                        permissionList.departmentRead;
                                        else noMeasuringDepartmentReadPermissionTemplate
                                    "
                                    [routerLink]="[
                                        '',
                                        'department',
                                        'detail',
                                        kpi?.measuringDepartment?.id
                                    ]"
                                >
                                    {{ kpi?.measuringDepartment?.name }}
                                </a>

                                <ng-template
                                    #noMeasuringDepartmentReadPermissionTemplate
                                >
                                    {{ kpi?.measuringDepartment?.name }}
                                </ng-template>
                            </td>
                        </tr>
                        <!-- Owning department-->
                        <tr>
                            <td>
                                {{ 'translate_owning_department' | translate }}
                            </td>
                            <td class="whitespace-pre-line">
                                <a
                                    *appHasPermissionId="
                                        permissionList.departmentRead;
                                        else noDepartmentReadPermissionTemplate
                                    "
                                    [routerLink]="[
                                        '',
                                        'department',
                                        'detail',
                                        kpi?.owningDepartment?.id
                                    ]"
                                >
                                    {{ kpi?.owningDepartment?.name }}
                                </a>

                                <ng-template
                                    #noDepartmentReadPermissionTemplate
                                >
                                    {{ kpi?.owningDepartment?.name }}
                                </ng-template>
                            </td>
                        </tr>

                        <!-- A description -->
                        <tr>
                            <td>
                                {{ 'translate_a_description' | translate }}
                            </td>
                            <td>
                                <app-value-loading
                                    [isLoading]="!kpi"
                                    [value]="kpi?.formulaDescriptionA"
                                ></app-value-loading>
                            </td>
                        </tr>

                        <!-- B description -->
                        <tr *ngIf="kpi?.formula.includes('B')">
                            <td>
                                {{ 'translate_b_description' | translate }}
                            </td>
                            <td>
                                <app-value-loading
                                    [isLoading]="!kpi"
                                    [value]="kpi?.formulaDescriptionB"
                                ></app-value-loading>
                            </td>
                        </tr>

                        <!--strategic goals -->
                        <tr>
                            <td>
                                {{ 'translate_strategic_goals' | translate }}
                            </td>
                            <td>
                                <app-list-loading [items]="kpi?.strategicGoals">
                                    <p
                                        *ngFor="
                                            let item of kpi?.strategicGoals ??
                                                []
                                        "
                                    >
                                        <a
                                            *appHasPermissionId="
                                                permissionList.strategicGoal;
                                                else noStrategicGoalReadPermissionTemplate
                                            "
                                            [routerLink]="[
                                                '',
                                                'strategic-goal',
                                                'detail',
                                                item.id
                                            ]"
                                        >
                                            {{ item.name }}
                                        </a>

                                        <ng-template
                                            #noStrategicGoalReadPermissionTemplate
                                        >
                                            {{ item.name }}
                                        </ng-template>
                                    </p>
                                </app-list-loading>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </ng-container>
        </app-content>

        <!-- PAST ACHIEVEMENTS -->
        <app-content
            contentTitle="{{ 'translate_past_achievements' | translate }}"
        >
            <div content class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <app-kpi-result-achieved-chart
                    [kpi]="kpi"
                ></app-kpi-result-achieved-chart>

                <app-kpi-result-target-chart
                    [kpi]="kpi"
                ></app-kpi-result-target-chart>
            </div>
        </app-content>

        <!-- OPERATIONS & BENCHMARKS -->
        <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
            <!-- OPERATIONS -->
            <app-content
                contentTitle="{{ 'translate_operations' | translate }}"
                class="mb-4"
            >
                <app-list-loading content [items]="kpi?.operations">
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>
                                        {{
                                            'translate_operation_name'
                                                | translate
                                        }}
                                    </th>
                                    <th>
                                        {{
                                            'translate_operation_level'
                                                | translate
                                        }}
                                    </th>
                                    <th>
                                        {{
                                            'translate_operation_owner'
                                                | translate
                                        }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let item of kpi?.operations">
                                    <td>
                                        <a
                                            *appHasPermissionId="
                                                permissionList.operationRead;
                                                else noOperationReadPermissionTemplate
                                            "
                                            [routerLink]="[
                                                '',
                                                'operation',
                                                'detail',
                                                item.id
                                            ]"
                                        >
                                            {{ item.name }}
                                        </a>

                                        <ng-template
                                            #noOperationReadPermissionTemplate
                                        >
                                            {{ item.name }}
                                        </ng-template>
                                    </td>
                                    <td>
                                        {{
                                            'translate_level_level'
                                                | translate
                                                    : { level: item.level }
                                        }}
                                    </td>
                                    <td>
                                        {{ item.ownerDepartment?.name }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </app-list-loading>
            </app-content>

            <!-- BENCHMARKS -->
            <app-kpi-benchmark-list
                #benchmarkList
                [kpiId]="kpiId"
            ></app-kpi-benchmark-list>
        </div>

        <!-- Operation procedures -->
        <app-content
            *ngIf="kpi?.operationProcedures?.length"
            contentTitle="{{ 'translate_operation_procedures' | translate }}"
        >
            <app-list-loading content [items]="kpi.operationProcedures">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>
                                    {{
                                        'translate_operation_procedure_name'
                                            | translate
                                    }}
                                </th>
                                <th>
                                    {{ 'translate_operation_name' | translate }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of kpi.operationProcedures">
                                <td>{{ item.name }}</td>
                                <td>{{ item.operation.name }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </app-list-loading>
        </app-content>

        <!-- Linked Capabilities -->
        <app-capability-linker
            [listLinkedCapabilitiesCallback]="listLinkedCapabilities()"
            [listUnlinkedCapabilitiesCallback]="listUnlinkedCapabilities()"
            [linkingCallback]="linkCapability()"
            [unlinkingCallback]="unlinkCapability()"
            [requiredPermissionForLinking]="permissionList.kpiWrite"
            [requiredPermissionForUnlinking]="permissionList.kpiWrite"
        >
        </app-capability-linker>

        <!-- Initial Results -->
        <app-content
            contentTitle="{{ 'translate_initial_result' | translate }}"
        >
            <ng-container
                content
                *ngIf="kpi?.initialResult !== null; else noInitialResult"
            >
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>
                                    {{ 'translate_year' | translate }}
                                </th>
                                <th>
                                    {{ 'translate_result' | translate }}
                                </th>
                                <th>
                                    {{
                                        'translate_initial_result_source'
                                            | translate
                                    }}
                                </th>
                                <th>
                                    {{
                                        'translate_initial_result_details'
                                            | translate
                                    }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="text-center">
                                    {{ kpi.creationYear }}
                                </td>
                                <td class="text-center">
                                    {{ kpi.initialResult }}
                                </td>
                                <td class="text-center">
                                    {{
                                        kpi.initialResultSource
                                            | translateItem
                                                : 'kpi-initial-result-source'
                                            | async
                                    }}
                                </td>
                                <td>{{ kpi.initialResultDetails }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </ng-container>
            <ng-template #noInitialResult>
                <app-items-are-unavailable></app-items-are-unavailable>
            </ng-template>
        </app-content>

        <!-- RESULTS -->
        <app-kpi-results [kpi]="kpi" (update)="refresh()"></app-kpi-results>

        <!-- EVALUATION SECTION -->
        <ng-container *ngIf="kpi">
            <app-evaluation-instance-list
                *appIsOrigin="[organizationOrigin.staging]"
                [item]="kpi"
                [type]="'kpi'"
            ></app-evaluation-instance-list>
        </ng-container>

        <!-- AGGREGATED ATTACHMENTS -->
        <app-kpi-attachment-list [kpiId]="kpiId"></app-kpi-attachment-list>

        <!-- REQUESTS -->
        <ng-container *appHasPermissionId="permissionList.kpi">
            <ng-container
                *appIsNotOrigin="[
                    organizationOrigin.police,
                    organizationOrigin.injaz
                ]"
            >
                <app-content
                    *ngIf="kpi"
                    [contentTitle]="
                        'translate_kpi_data_entry_responses' | translate
                    "
                >
                    <app-responses-table
                        content
                        [shownFields]="{
                            year: true,
                            department: true,
                            kpi: false,
                            stage: true,
                            time: true
                        }"
                        [tableController]="responsesTableController"
                    ></app-responses-table>
                </app-content>
            </ng-container>
        </ng-container>
    </div>
</app-page>

<ng-template #badgeTemplate let-value="value" let-title="title">
    <div class="my-1 flex items-center overflow-hidden rounded-sm shadow">
        <p
            class="whitespace-nowrap bg-gray-100 px-2 py-1 text-sm text-gray-500"
        >
            {{ title | translate }}
        </p>
        <p
            [appTooltip]="title | translate"
            class="w-full bg-primary-500 px-4 py-1 text-sm text-white"
        >
            {{ value }}
        </p>
    </div>
</ng-template>
