import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { permissionList } from '@masar/common/constants';
import { MnmFormState } from '@masar/shared/components';
import { KpiResultRequestService } from '../../../kpi-result-request.service';
import { fields } from './fields';
import { TranslateService } from '@ngx-translate/core';
import { finalize } from 'rxjs/operators';
import { NotificationService } from 'mnm-webapp';
import { KpiResultRequestComment } from '@masar/common/models';

@Component({
    selector: 'app-request-new',
    templateUrl: 'comment-new.component.html',
})
export class CommentNewComponent implements OnInit {
    @Input() public requestId: string;
    @Input() public commentId: string;

    @Output()
    public commentCreated: EventEmitter<KpiResultRequestComment> =
        new EventEmitter<KpiResultRequestComment>();

    @Output()
    public commentUpdated: EventEmitter<KpiResultRequestComment> =
        new EventEmitter<KpiResultRequestComment>();

    public isSubmitting = false;
    public formState: MnmFormState;
    public permissionList = permissionList;
    public mode: 'edit' | 'new' = 'new';

    public constructor(
        private translateService: TranslateService,
        private kpiRequestService: KpiResultRequestService,
        private notificationService: NotificationService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);
    }

    public ngOnInit(): void {
        switch (this.mode) {
            case 'edit':
                this.kpiRequestService
                    .getComment(this.commentId)
                    .subscribe(item => {
                        this.fillForm(item);
                    });
                break;
        }
    }

    public reset(): void {
        this.formState.reset();
        this.requestId = '';
    }

    public submit(): boolean | Promise<boolean> {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return false;
        }

        this.isSubmitting = true;

        const observable =
            this.mode === 'edit'
                ? this.kpiRequestService.updateComment(
                      this.formState.group.getRawValue()
                  )
                : this.kpiRequestService.createComment(
                      this.formState.group.getRawValue(),
                      this.requestId
                  );

        return observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .toPromise()
            .then(item => {
                this.translateService
                    .get('translate_item_added_successfully')
                    .subscribe(m => this.notificationService.notifySuccess(m));
                if (this.mode === 'new') {
                    this.commentCreated.emit(item);
                } else if (this.mode === 'edit') {
                    this.commentUpdated.emit(item);
                }
                return true;
            })
            .catch(() => {
                return false;
            });
    }

    private fillForm(item: KpiResultRequestComment): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }
}
