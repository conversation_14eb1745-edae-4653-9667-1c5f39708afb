<app-page
    pageTitle="{{
        (mode === 'new'
            ? 'translate_add_new_kpi'
            : 'translate_update_existing_kpi'
        ) | translate
    }}"
>
    <!-- Tools -->
    <ng-container tools>
        <!-- Preview -->
        <a
            *ngIf="formState.group.controls['id'].value"
            [routerLink]="[
                '',
                'kpi',
                'detail',
                formState.group.controls['id'].value
            ]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-eye me-2"></i>
            <span>{{ 'translate_preview' | translate }}</span>
        </a>

        <!-- Back -->
        <a [routerLink]="['', 'kpi']" class="btn btn-sm btn-outline-white">
            <i class="fa-light fa-share me-2"></i>
            <span>{{ 'translate_all_kpis' | translate }}</span>
        </a>
    </ng-container>

    <!-- Content -->
    <ng-container content>
        <span
            *ngIf="kpiFullNumber"
            class="mb-4 inline-block rounded-xl bg-primary px-2 py-1 text-xs text-white"
        >
            {{ kpiCodePrefix + '-' + kpiFullNumber }}
        </span>
        <mnm-form
            *ngIf="formState"
            [state]="formState"
            [translateLabels]="true"
        >
            <div class="mt-2 text-center">
                <button
                    type="submit"
                    class="btn-lg btn btn-primary"
                    (click)="submit()"
                    [disabled]="isSubmitting"
                >
                    <app-loading-ring
                        *ngIf="isSubmitting"
                        class="me-2"
                    ></app-loading-ring>
                    <i class="fa-light fa-save me-2"></i>
                    <span>{{ 'translate_save' | translate }}</span>
                </button>
            </div>
        </mnm-form>
    </ng-container>
</app-page>
<ng-template #measuringDepartmentFieldRef *ngIf="isMeasuringDepartmentEnabled">
    <div style="flex: 6 1 0">
        <label class="mb-2 inline-block">
            {{ 'translate_kpi_measuring_department' | translate }}
            <span
                class="text-red-500"
                *ngIf="isFieldRequired('measuringDepartment')"
                >*</span
            >
        </label>
        <app-tree-select
            [formControl]="
                $any(formState.group.controls['measuringDepartment'])
            "
            [childrenFetcher]="measuringDepartmentChildrenFetcher"
            [parentFetcher]="measuringDepartmentParentFetcher"
        >
        </app-tree-select>
        <span class="whitespace-normal break-words text-sm text-gray-400">{{
            'translate_kpi_measuring_department_description' | translate
        }}</span>
        <div
            class="mt-1 text-xs text-red-500"
            *ngIf="formState.triedToSubmit && $any(formState.group.controls['measuringDepartment']).invalid && $any(formState.group.controls['measuringDepartment']).errors?.['required']"
        >
            {{ 'translate_this_field_is_mandatory' | translate }}
        </div>
    </div>
</ng-template>
<ng-template #departmentListFieldRef>
    <app-fieldset legend="{{ 'translate_linked_departments' | translate }}">
        <app-item-list-field
            [formControl]="$any(formState.group.controls['departments'])"
            [parentFetcher]="departmentParentFetcher"
            [childrenFetcher]="departmentChildrenFetcher"
        ></app-item-list-field>
    </app-fieldset>
</ng-template>

<ng-template #operationListFieldRef>
    <app-fieldset legend="{{ 'translate_linked_operations' | translate }}">
        <app-item-list-field
            [formControl]="$any(formState.group.controls['operations'])"
            [parentFetcher]="operationParentFetcher"
            [childrenFetcher]="operationChildrenFetcher"
        ></app-item-list-field>
    </app-fieldset>
</ng-template>

<!-- Operation procedure -->
<ng-template #operationProcedureFieldRef>
    <ng-select
        class="col-span-4"
        [items]="operationProceduresLoader.items$ | async"
        [loading]="operationProceduresLoader.itemsLoading"
        [typeahead]="operationProceduresLoader.itemInput$"
        (open)="operationProceduresLoader.loadInitialList()"
        [multiple]="true"
        bindLabel="name"
        [formControl]="$any(formState.group.controls['operationProcedures'])"
    >
        <ng-template ng-label-tmp let-item="item" let-clear="clear">
            <span class="ng-value-icon left" (click)="clear(item)">×</span>

            <span class="ng-value-label">
                {{ item.name + ' (' + item.operation.name + ') ' }}
            </span>
        </ng-template>

        <ng-template ng-option-tmp let-item="item">
            {{ item.name + ' (' + item.operation.name + ') ' }}
        </ng-template>
    </ng-select>
</ng-template>
