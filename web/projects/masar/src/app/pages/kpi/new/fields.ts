import { MnmFormField } from '@masar/shared/components';
import { Validators } from '@angular/forms';
import { isNotOrigin } from '@masar/common/utils';
import { OrganizationOrigin } from '@masar/common/enums';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },
    {
        fields: [
            {
                name: 'code',
                type: 'text',
                label: 'translate_kpi_number',
                size: 2,
                validators: [Validators.required],
            },

            {
                name: 'type',
                type: 'select',
                label: 'translate_kpi_type',
                size: 2,
                bindLabel: 'name',
                validators: [Validators.required],
            },

            {
                name: 'units',
                type: 'select',
                label: 'translate_measuring_unit',
                size: 3,
                bindLabel: 'name',
                bindValue: 'id',
                validators: [Validators.required],
                compareWith: (a, b) => a.id === b,
            },

            {
                name: 'formula',
                type: 'select',
                label: 'translate_formula',
                size: 3,
                bindLabel: 'name',
                bindValue: 'id',
                validators: [Validators.required],
                compareWith: (a, b) => a.id === b,
            },

            {
                name: 'decimalPlaces',
                type: 'number',
                label: 'translate_decimal_places',
                size: 2,
                defaultValue: 2,
                minValue: 2,
                isIntegerOnly: true,
                hide: true,
                validators: [Validators.min(2), Validators.max(99)],
            },
        ],
    },

    {
        fields: [
            {
                name: 'nameAr',
                type: 'text',
                label: 'translate_kpi_name_in_arabic',
                size: 6,
                validators: [
                    Validators.required,
                    Validators.maxLength(256),
                    Validators.minLength(3),
                ],
            },

            {
                name: 'nameEn',
                type: 'text',
                label: 'translate_kpi_name_in_english',
                size: 6,
            },
        ],
    },
    {
        fields: [
            {
                name: 'descriptionAr',
                type: 'textarea',
                label: 'translate_description_in_arabic',
                size: 6,
                validators: [Validators.minLength(3)],
            },

            {
                name: 'descriptionEn',
                type: 'textarea',
                label: 'translate_description_in_english',
                size: 6,
            },
        ],
    },

    {
        fields: [
            {
                name: 'measurementCycle',
                type: 'select',
                label: 'translate_measuring_cycle',
                size: 1,
                bindLabel: 'name',
                bindValue: 'id',
                validators: [Validators.required],
                compareWith: (a, b) => a.id === b,
            },
        ],
    },

    {
        fields: [
            {
                name: 'formulaDescriptionAAr',
                type: 'text',
                label: 'translate_a_description_in_arabic',
                size: 3,
                validators: [
                    Validators.required,
                    Validators.minLength(3),
                    Validators.maxLength(1024),
                ],
            },

            {
                name: 'formulaDescriptionBAr',
                type: 'text',
                label: 'translate_b_description_in_arabic',
                size: 3,
                validators: [
                    Validators.required,
                    Validators.minLength(3),
                    Validators.maxLength(1024),
                ],
            },

            {
                name: 'formulaDescriptionAEn',
                type: 'text',
                label: 'translate_a_description_in_english',
                size: 3,
                validators: [
                    Validators.minLength(3),
                    Validators.maxLength(1024),
                ],
            },

            {
                name: 'formulaDescriptionBEn',
                type: 'text',
                label: 'translate_b_description_in_english',
                size: 3,
                validators: [
                    Validators.minLength(3),
                    Validators.maxLength(1024),
                ],
            },
        ],
    },

    {
        fields: [
            {
                name: 'dataEntryMethod',
                label: 'translate_data_entry_method',
                type: 'select',
                bindLabel: 'name',
                bindValue: 'id',
                defaultValue: 'upon_request',
                compareWith: (a, b) => a.id === b,
                size: 6,
                notClearable: true,
                notSearchable: true,
                validators: [Validators.required],
            },

            {
                name: 'aggregationTypeA',
                label: 'translate_aggregation_type_for_a',
                type: 'select',
                bindLabel: 'name',
                bindValue: 'id',
                compareWith: (a, b) => a.id === b,
                size: 6,
                notClearable: true,
                notSearchable: true,
                validators: [Validators.required],
            },

            {
                name: 'aggregationTypeB',
                label: 'translate_aggregation_type_for_b',
                type: 'select',
                bindLabel: 'name',
                bindValue: 'id',
                compareWith: (a, b) => a.id === b,
                size: 6,
                notClearable: true,
                notSearchable: true,
                validators: [Validators.required],
            },
        ],
    },

    {
        fields: [
            {
                name: 'decreaseIsBest',
                type: 'select',
                label: 'translate_kpi_pattern',
                size: 4,
                bindValue: 'id',
                bindLabel: 'name',
                compareWith: (a, b) => a.id === b,
                defaultValue: false,
            },
            {
                name: 'creationYear',
                type: 'select',
                label: 'translate_creation_year',
                size: 4,
                bindLabel: 'name',
                bindValue: 'id',
                validators: [Validators.required],
                compareWith: (a, b) => a.id === b,
            },
            {
                name: 'source',
                type: 'select',
                label: 'translate_kpi_source',
                size: 4,
                bindLabel: 'name',
                bindValue: 'id',
                validators: [Validators.required],
                compareWith: (a, b) => a.id === b,
            },
        ],
    },

    {
        fields: [
            {
                name: 'tags',
                type: 'select',
                label: 'translate_kpi_categorization',
                bindLabel: 'name',
                size: 6,
                multiple: true,
            },

            {
                name: 'balancedBehaviorCard',
                type: 'select',
                label: 'translate_balanced_behavior_card',
                size: 6,
                bindLabel: 'name',
            },
        ],
    },

    {
        name: 'strategicGoals',
        type: 'select',
        label: 'translate_linked_strategic_goals',
        multiple: true,
        bindLabel: 'name',
        isDisabled: false,
        groupBy: 'yearRange',
    },

    {
        fields: [
            {
                name: 'initialResultSource',
                type: 'select',
                label: 'translate_initial_result_source',
                size: 4,
                bindLabel: 'name',
                bindValue: 'id',
                compareWith: (a, b) => a.id === b,
            },

            {
                name: 'initialResult',
                type: 'number',
                label: 'translate_initial_result',
                size: 4,
            },

            {
                name: 'initialResultDetails',
                type: 'textarea',
                label: 'translate_initial_result_details',
                size: 4,
            },
        ],
    },
    {
        fields: [
            {
                name: 'isTrend',
                type: 'checkbox',
                label: 'translate_is_statistical_kpi',
                size: 4,
                defaultValue: false,
            },
            {
                name: 'isSpecial',
                type: 'checkbox',
                label: 'translate_is_special',
                size: 4,
                hide: isNotOrigin([
                    OrganizationOrigin.police,
                    OrganizationOrigin.localhost,
                ]),
                defaultValue: false,
            },
        ],
    },
    {
        name: 'measuringDepartment',
        size: 6,
    },
    {
        name: 'departments',
    },

    {
        name: 'owningDepartment',
        type: 'select',
        label: 'translate_kpi_owning_department',
        bindLabel: 'name',
        notClearable: true,
        validators: [Validators.required],
    },

    {
        name: 'operations',
    },

    {
        name: 'operationProcedures',
        label: 'translate_operation_procedures',
    },
];
