import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    ElementRef,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { MnmFormField, MnmFormState } from '@masar/shared/components';
import { ActivatedRoute } from '@angular/router';
import { NotificationService } from 'mnm-webapp';
import { FormBuilder, Validators } from '@angular/forms';
import { fields } from './fields';
import { finalize, first, takeUntil } from 'rxjs/operators';
import { KpiService } from '../kpi.service';
import { TranslateService } from '@ngx-translate/core';
import {
    Item,
    Kpi,
    KpiType,
    Operation,
    OperationProcedure,
} from '@masar/common/models';
import {
    AppSettingFetcherService,
    HelperService,
    MiscApiService,
} from '@masar/core/services';
import { Observable, of, Subject } from 'rxjs';
import { kpiFunctions } from '../kpiFunctions';
import { Loader } from '@masar/common/misc/loader';
import { optionalFieldAndSectionHandler } from '@masar/common/utils';

@Component({
    selector: 'app-new',
    templateUrl: './new.component.html',
})
export class NewComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('departmentListFieldRef')
    public departmentListFieldRef: ElementRef;
    @ViewChild('measuringDepartmentFieldRef')
    public measuringDepartmentFieldRef: ElementRef;
    @ViewChild('kpiDirectionRef') public kpiDirectionRef: ElementRef;
    @ViewChild('operationListFieldRef')
    public operationListFieldRef: ElementRef;
    @ViewChild('operationProcedureFieldRef')
    public operationProcedureFieldRef: ElementRef;

    public isSubmitting = false;
    public isMeasuringDepartmentEnabled: boolean = false;
    public mode: 'new' | 'edit';

    public formState: MnmFormState;
    public kpiFullNumber: string;

    public departmentParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;
    public departmentChildrenFetcher: (parentId: string) => Observable<Item[]>;

    public measuringDepartmentParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;
    public measuringDepartmentChildrenFetcher: (
        parentId: string
    ) => Observable<Item[]>;

    public operationParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;
    public operationChildrenFetcher: (parentId: string) => Observable<Item[]>;

    public operationProceduresLoader: Loader<OperationProcedure>;

    public kpiCodePrefix: string = '';
    private measurementMethods: Item[];

    private unsubscribeAll = new Subject();
    public constructor(
        private notificationService: NotificationService,
        private kpiService: KpiService,
        private translateService: TranslateService,
        private activatedRoute: ActivatedRoute,
        private miscApiService: MiscApiService,
        private readonly helperService: HelperService,
        private appSettingFetcherService: AppSettingFetcherService,
        private changeDetectorRef: ChangeDetectorRef,
        fb: FormBuilder
    ) {
        this.measuringDepartmentParentFetcher = (childId: string) =>
            miscApiService.parentDepartment(childId);

        this.measuringDepartmentChildrenFetcher = (parentId: string) =>
            miscApiService.departments({
                parentDepartmentId: parentId,
                respectHierarchy: true,
                scope: 'all',
            });
        this.departmentParentFetcher = (childId: string) =>
            miscApiService.parentDepartment(childId);
        this.departmentChildrenFetcher = (parentId: string) =>
            miscApiService.departments({
                parentDepartmentId: parentId,
                respectHierarchy: true,
            });

        this.operationParentFetcher = (childId: string) =>
            miscApiService.parentOperation(childId);
        this.operationChildrenFetcher = (parentId: string) =>
            miscApiService.operations({
                parentOperationId: parentId,
                respectHierarchy: true,
            });

        this.formState = new MnmFormState(fields(), fb);
        this.updateOptionalSectionsAndFields();

        this.operationProceduresLoader = new Loader<OperationProcedure>(
            keyword => {
                // First check which operations were selected.
                const operations: Operation[] =
                    this.formState.group.controls['operations'].value;

                // If none, then the list should be empty.
                if (!operations || operations.length === 0) {
                    return of([]);
                }

                // Else fetch procedures under the selected operations.
                return miscApiService.operationProcedures({
                    keyword,
                    operationIds: operations.map(x => x.id),
                });
            }
        );

        appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(({ appId }) => (this.kpiCodePrefix = appId));

        miscApiService.kpiTags().subscribe(items => {
            this.formState.get('tags').items = items;
        });

        miscApiService
            .kpiTypes()
            .subscribe(items => (this.formState.get('type').items = items));

        this.formState.get('decreaseIsBest').items = [
            {
                id: true,
                name: translateService.instant('translate_decrease_is_best'),
            },

            {
                id: false,
                name: translateService.instant('translate_increase_is_best'),
            },
        ];

        miscApiService.setMiscItems(this.formState, [
            ['kpi-initial-result-source', 'initialResultSource'],
            ['kpi-result-aggregation-type', 'aggregationTypeA'],
            ['kpi-result-aggregation-type', 'aggregationTypeB'],
            ['kpi-creation-year', 'creationYear'],
            ['kpi-source', 'source'],
            ['kpi-data-entry-method', 'dataEntryMethod'],
            ['kpi-cycle', 'measurementCycle'],
            ['kpi-result-calculation-formula', 'formula'],
            ['kpi-unit', 'units'],
            ['kpi-balanced-behavior-card', 'balancedBehaviorCard'],
            ['strategic-goal', 'strategicGoals', undefined, true],
        ]);
    }

    public ngOnInit(): void {
        // Has to be loaded here. The filling/creating the form
        // relies on the form being monitored. the form being monitored
        // relies on the measurement methods being loaded.
        this.miscApiService
            .getList('kpi-calculation-method')
            .subscribe(items => {
                this.measurementMethods = items;
                this.monitorForm();
                this.setupForm();
            });
    }

    public ngAfterViewInit(): void {
        this.formState.get('departments').customField =
            this.departmentListFieldRef;
        this.formState.get('measuringDepartment').customField =
            this.measuringDepartmentFieldRef;

        this.formState.get('operations').customField =
            this.operationListFieldRef;

        this.formState.get('operationProcedures').customInputField =
            this.operationProcedureFieldRef;
    }

    public ngOnDestroy(): void {
        this.operationProceduresLoader.dispose();

        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public submit(): void {
        this.formState.setTriedToSubmit();
        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const observable =
            this.mode === 'new'
                ? this.kpiService.create(this.formState.group.getRawValue())
                : this.kpiService.update(this.formState.group.getRawValue());

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(kpi => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'kpi'],
                    kpi.id
                );
            });
    }

    public isFieldRequired(fieldName: string): boolean {
        const control = this.formState.group.get(fieldName);
        if (!control) return false;

        const validator = control.validator?.(control);
        return validator?.['required'] !== undefined;
    }
    private setupForm(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[0].path) {
                case 'new':
                    this.mode = 'new';

                    // Get the auto incremented code.
                    this.kpiService.getNextCode().subscribe(code => {
                        this.formState.group.get('code').setValue(code);
                    });
                    kpiFunctions.syncForm(
                        this.formState,
                        this.measurementMethods,
                        [4, 1]
                    );

                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            const id = params.id;
                            this.kpiService.get(id, true).subscribe(item => {
                                this.fillForm(item);
                                this.fillForm(item); // To fill the fields that show up after the first pass.
                                kpiFunctions.syncForm(
                                    this.formState,
                                    this.measurementMethods,
                                    [4, 1],
                                    item.isTrend
                                );
                            });
                        });
                    break;
            }
        });
    }

    private fillForm(item: Kpi): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }

    private setKpiFullNumber(code: string, type: KpiType): void {
        if (!type || !code) {
            this.kpiFullNumber = '';
        } else {
            this.kpiFullNumber = `${type.code}-${code}`;
        }
    }

    private monitorForm(): void {
        kpiFunctions.monitorForm(
            this.formState,
            this.measurementMethods,
            [4, 1],
            this.unsubscribeAll
        );

        // Monitor kpi code & type fields.
        this.formState.group.controls.type.valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe((type: KpiType) => {
                // Stuff for updating the badge that contains
                // the kpi full code at the top of the form.
                const code = this.formState.group.get('code').value;
                this.setKpiFullNumber(code, type);
            });

        // Monitor the code for the badge.
        this.formState.group.controls.code.valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe((code: string) => {
                const type = this.formState.group.get('type').value;
                this.setKpiFullNumber(code, type);
            });

        // Monitor kpi source.
        const removePreviousSourceSubscription = new Subject();
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(settings => {
                const { kpiSetting } = settings;
                const entityField: MnmFormField = {
                    name: 'entity',
                    label: 'translate_entity',
                    type: 'text',
                    size: 3,
                };

                removePreviousSourceSubscription.next();
                this.formState.removeField('entity');

                if (kpiSetting.isDecimalPlacesEnabled)
                    this.formState.showField('decimalPlaces');
                else this.formState.hideField('decimalPlaces');

                if (!kpiSetting.canHaveEntityForInternalSources) {
                    entityField.validators = [Validators.required];

                    this.formState.group
                        .get('source')
                        .valueChanges.pipe(
                            takeUntil(removePreviousSourceSubscription)
                        )
                        .subscribe(source => {
                            this.formState.removeField('entity');
                            if (source === 'external') {
                                this.formState.addField(entityField, 7, 6);
                            }
                        });
                } else {
                    entityField.validators = [];
                    this.formState.addField(entityField, 7, 6);
                }
            });
        this.unsubscribeAll.pipe(first()).subscribe(() => {
            removePreviousSourceSubscription.next();
            removePreviousSourceSubscription.complete();
        });

        // Monitor departments:
        this.formState.group.controls['departments'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe((departments: Item[]) => {
                const owningDepartmentControl =
                    this.formState.group.controls['owningDepartment'];
                const owningDepartmentField =
                    this.formState.get('owningDepartment');

                const selectedDepartment: Item = owningDepartmentControl.value;

                if (departments.length === 0) {
                    owningDepartmentControl.setValue(null);
                } else {
                    if (
                        departments.findIndex(
                            x => x.id === selectedDepartment?.id
                        ) === -1
                    ) {
                        owningDepartmentControl.setValue(departments[0]);
                    }
                    owningDepartmentField.items = departments.filter(
                        (v, i, a) => a.findIndex(x => x.id === v.id) === i
                    );
                }
            });

        // Monitor operations.
        this.formState.group.controls['operations'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe((selectedOperations: Operation[]) => {
                // Get currently selected procedures.
                const selectedOperationProcedures: OperationProcedure[] =
                    this.formState.group.controls['operationProcedures'].value;

                // Figure out which of the currently selected should
                // remain selected upon the new selection of operations.
                const selectedOperationIds = selectedOperations.map(x => x.id);
                const validOperationProcedures =
                    selectedOperationProcedures?.filter(x =>
                        selectedOperationIds.includes(x.operation.id)
                    ) ?? [];

                // Update the set of selected procedures.
                this.formState.group.controls['operationProcedures'].setValue(
                    validOperationProcedures
                );
            });
    }
    private updateOptionalSectionsAndFields(): void {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(item => {
                const optionalFields = item.kpiSetting.optionalFields;

                // Find the measuring department field settings
                const measuringDepartmentField = optionalFields.find(
                    (field: any) => field.name === 'measuring_department'
                );

                // Set the enabled state
                this.isMeasuringDepartmentEnabled =
                    measuringDepartmentField?.isEnabled ?? true;

                // Update form control validators based on isRequired
                if (this.isMeasuringDepartmentEnabled) {
                    const control = this.formState.group.get(
                        'measuringDepartment'
                    );
                    if (measuringDepartmentField?.isRequired) {
                        control.setValidators([Validators.required]);
                    } else {
                        control.setValidators(null);
                    }
                    control.updateValueAndValidity();
                }

                optionalFieldAndSectionHandler(
                    this.formState,
                    this.changeDetectorRef,
                    {
                        optionalFields: optionalFields,
                        fields: [
                            ['measuring_department', 'measuringDepartment'],
                        ],
                    },
                    null
                );
            });
    }
}
