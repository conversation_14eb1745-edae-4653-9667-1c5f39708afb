import { Component, NgModuleRef } from '@angular/core';
import { TableController } from '@masar/common/misc/table';
import { KpiDynamicDataEntryRequest } from '@masar/common/models';
import { KpiDynamicDataEntryRequestService } from '@masar/pages/kpi/kpi-dynamic-data-entry-request.service';
import { ModalService } from 'mnm-webapp';
import { FlowTransactionHistoryComponent } from '@masar/features/flow/components';
import { TranslateService } from '@ngx-translate/core';
import { LibraryFileLinkerComponent } from '@masar/features/masar/components';

interface DynamicDataEntryRequestFilter {
    keyword: string;
    status: '' | 'pending' | 'approved';
}

@Component({
    selector: 'app-dynamic-data-entry-list',
    templateUrl: './dynamic-data-entry-request-list.component.html',
})
export class DynamicDataEntryRequestListComponent {
    public tableController: TableController<
        KpiDynamicDataEntryRequest,
        DynamicDataEntryRequestFilter
    >;

    public constructor(
        private modalService: ModalService,
        private moduleRef: NgModuleRef<any>,
        private translateService: TranslateService,
        private kpiDynamicDataEntryRequestService: KpiDynamicDataEntryRequestService
    ) {
        this.tableController = new TableController<
            KpiDynamicDataEntryRequest,
            DynamicDataEntryRequestFilter
        >(
            filter =>
                kpiDynamicDataEntryRequestService.list(
                    filter.data.keyword,
                    filter.data.status,
                    filter.pageNumber,
                    filter.pageSize
                ),
            {
                data: {
                    keyword: '',
                    status: '',
                },
            }
        );

        this.tableController.start();
    }

    public async showFlowTransactionHistoryDialog(id: string): Promise<void> {
        await this.modalService.show(FlowTransactionHistoryComponent, {
            title: this.translateService.instant(
                'translate_flow_transaction_history'
            ),
            beforeInit: c => {
                c.itemId = id;
                c.itemType = 'kpi-dynamic-data-entry-request';
                c.isWrapped = false;
            },
            moduleRef: this.moduleRef,
        });
    }

    public async showFileLinkerDialog(id: string): Promise<void> {
        await this.modalService.show(LibraryFileLinkerComponent, {
            moduleRef: this.moduleRef,
            size: { width: '80%' },
            beforeInit: c => {
                c.listLinkedFilesCallback = (
                    keyword: string,
                    pageNumber: number,
                    pageSize: number
                ) =>
                    this.kpiDynamicDataEntryRequestService.listLinkedLibraryFiles(
                        id,
                        keyword,
                        pageNumber,
                        pageSize
                    );
                c.listUnlinkedLibraryFilesCallback = (
                    keyword: string,
                    pageNumber: number,
                    pageSize: number
                ) =>
                    this.kpiDynamicDataEntryRequestService.listUnlinkedLibraryFiles(
                        id,
                        keyword,
                        pageNumber,
                        pageSize
                    );
                c.linkingCallback = (fileId: string) =>
                    this.kpiDynamicDataEntryRequestService.linkLibraryFile(
                        id,
                        fileId
                    );

                c.unlinkingCallback = (fileId: string) =>
                    this.kpiDynamicDataEntryRequestService.unlinkLibraryFile(
                        id,
                        fileId
                    );
            },
        });
    }
}
