<app-page pageTitle="{{ 'translate_dynamic_data_entry_requests' | translate }}">
    <ng-container content>
        <!-- Filter -->
        <app-filter-result-box>
            <!-- Keyword field -->
            <app-search-input
                [(ngModel)]="tableController.filter.data.keyword"
                [tableController]="tableController"
            ></app-search-input>

            <!-- Status -->
            <ng-select
                [items]="[
                    {
                        id: '',
                        name: 'translate_all' | translate
                    },
                    {
                        id: 'pending',
                        name: ('translate_approval_status_pending' | translate)
                    },

                    {
                        id: 'approved',
                        name: ('translate_approval_status_approved' | translate)
                    }
                ]"
                bindValue="id"
                bindLabel="name"
                placeholder="{{ 'translate_approval_status' | translate }}"
                [clearable]="false"
                [searchable]="false"
                [(ngModel)]="tableController.filter.data.status"
                (change)="tableController.filter$.next(true)"
            >
            </ng-select>
        </app-filter-result-box>

        <app-list-loading [items]="tableController.items">
            <table class="mb-5">
                <thead>
                    <tr>
                        <th>{{ 'translate_result' | translate }}</th>
                        <th>{{ 'translate_measurement_cycle' | translate }}</th>
                        <th>{{ 'translate_period' | translate }}</th>
                        <th>A</th>
                        <th>B</th>
                        <th>{{ 'translate_result' | translate }}</th>
                        <th>{{ 'translate_achieved' | translate }}</th>
                        <th>{{ 'translate_state' | translate }}</th>
                        <th style="width: 0%">
                            <i class="fa-light fa-gear"></i>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of tableController.items">
                        <td>
                            <div class="flex flex-col">
                                <!-- Kpi name -->
                                <span>
                                    <app-kpi-link
                                        [item]="item.period.kpiResult.kpi"
                                    ></app-kpi-link>
                                </span>

                                <span class="text-xs">
                                    <app-department-link
                                        [item]="
                                            item.period.kpiResult.department
                                        "
                                    ></app-department-link
                                    >, {{ item.period.kpiResult.year }}
                                </span>

                                <!-- B description -->
                                <span class="text-xs font-bold text-gray-400">
                                    A:
                                    {{
                                        item.period.kpiResult
                                            .formulaDescriptionA
                                    }}
                                </span>

                                <!-- B description -->
                                <span
                                    *ngIf="
                                        item.period.kpiResult.formula.includes(
                                            'B'
                                        )
                                    "
                                    class="text-xs font-bold text-gray-400"
                                >
                                    B:
                                    {{
                                        item.period.kpiResult
                                            .formulaDescriptionB
                                    }}
                                </span>
                            </div>
                        </td>

                        <!-- Measurement cycle -->
                        <td class="text-center">
                            {{
                                item.period.kpiResult.measurementCycle
                                    | translateItem : 'kpi-cycle'
                                    | async
                            }}
                        </td>

                        <!-- Period -->
                        <td class="text-center">
                            {{ item.period.period + 1 }}
                        </td>

                        <!-- A -->
                        <td>
                            <div class="flex flex-col">
                                <span
                                    class="text-center text-lg font-bold text-emerald-700"
                                    >{{ item.a }}</span
                                >
                                <span class="text-xs font-bold text-gray-400">{{
                                    'translate_current_value_value'
                                        | translate
                                            : { value: item.period.a ?? '-' }
                                }}</span>
                            </div>
                        </td>

                        <!-- B -->
                        <td>
                            <div class="flex flex-col">
                                <span
                                    class="text-center text-lg font-bold text-emerald-700"
                                    >{{ item.b }}</span
                                >
                                <span class="text-xs font-bold text-gray-400">{{
                                    'translate_current_value_value'
                                        | translate
                                            : { value: item.period.b ?? '-' }
                                }}</span>
                            </div>
                        </td>

                        <!-- Result -->
                        <td>
                            <div class="flex flex-col">
                                <span class="text-center">
                                    {{
                                        item.result
                                            | round : 2
                                            | formatKpiResult
                                                : item.period.kpiResult.units
                                                : item.period.kpiResult
                                                      .unitsDescription
                                    }}
                                </span>
                                <span class="text-xs font-bold text-gray-400">{{
                                    'translate_current_value_value'
                                        | translate
                                            : {
                                                  value:
                                                      (item.period.result
                                                          | round : 2) ?? '-'
                                              }
                                }}</span>
                            </div>
                        </td>

                        <!-- Achieved -->
                        <td>
                            <div class="flex flex-col gap-1">
                                <span>
                                    <app-achieved-container
                                        [achieved]="item.achieved"
                                    ></app-achieved-container>
                                </span>
                                <span class="text-xs font-bold text-gray-400">{{
                                    'translate_current_value_value'
                                        | translate
                                            : {
                                                  value:
                                                      item.achieved === null
                                                          ? '-'
                                                          : (item.period
                                                                .achieved * 100
                                                            | round : 2)
                                              }
                                }}</span>
                            </div>
                        </td>

                        <!-- State -->
                        <td class="text-center">
                            {{
                                'translate_flow_state_' + item.flowState
                                    | translate
                            }}
                        </td>

                        <!-- Controls -->
                        <td>
                            <app-dropdown>
                                <!-- Flow -->
                                <app-flow-move-button
                                    [item]="item"
                                    itemType="kpi-dynamic-data-entry-request"
                                    (transfer)="
                                        tableController.filter$.next(false)
                                    "
                                ></app-flow-move-button>

                                <!-- Library files -->
                                <button
                                    (click)="showFileLinkerDialog(item.id)"
                                    class="btn btn-sm btn-success"
                                    [appTooltip]="
                                        'translate_attachments' | translate
                                    "
                                >
                                    <i class="fa-light fa-paperclip fa-fw"></i>
                                    <!--                                    {{ 'translate_attachments' | translate }}-->
                                </button>

                                <!-- Flow history -->
                                <button
                                    (click)="
                                        showFlowTransactionHistoryDialog(
                                            item.id
                                        )
                                    "
                                    class="btn btn-sm btn-info"
                                    [appTooltip]="
                                        'translate_transfer_history' | translate
                                    "
                                >
                                    <i
                                        class="fa-light fa-clock-rotate-left fa-fw"
                                    ></i>
                                    <!--                                    {{'translate_transfer_history' | translate }}-->
                                </button>
                            </app-dropdown>
                        </td>
                    </tr>
                </tbody>
            </table>
            <app-table-pagination
                [tableController]="tableController"
            ></app-table-pagination>
        </app-list-loading>
    </ng-container>
</app-page>
