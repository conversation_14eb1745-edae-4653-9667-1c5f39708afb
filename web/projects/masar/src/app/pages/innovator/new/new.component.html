<app-page
    pageTitle="{{
        (mode === 'new'
            ? 'translate_add_new_innovator'
            : 'translate_update_existing_innovator'
        ) | translate
    }}"
>
    <!-- Tools -->
    <ng-container tools>
        <!-- List -->
        <a
            [routerLink]="['', 'innovator']"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-share me-2"></i>
            <span>{{ 'translate_innovators_list' | translate }}</span>
        </a>

        <!-- Detail -->
        <a
            *ngIf="mode === 'edit'"
            [routerLink]="[
                '',
                'innovator',
                'detail',
                formState.group.controls['id'].value
            ]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-eye me-2"></i>
            <span>{{ 'translate_preview' | translate }}</span>
        </a>
    </ng-container>

    <div content>
        <!-- Content -->
        <div *ngIf="employeeNumber" class="mb-4">
            <div
                class="rounded-b border-t-4 px-4 py-3 shadow-md"
                [ngClass]="{
                    'border-green-500 bg-green-100 text-green-900': isUser,
                    'border-blue-500 bg-blue-100 text-blue-900': !isUser
                }"
                role="alert"
            >
                <div class="flex">
                    <div class="py-1">
                        <svg
                            class="me-4 h-6 w-6 fill-current text-blue-500"
                            [class.text-blue-500]="!isUser"
                            [class.text-green-500]="isUser"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                        >
                            <path
                                d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"
                            />
                        </svg>
                    </div>
                    <div *ngIf="isUser">
                        <p class="font-bold">
                            {{ 'translate_alert_is_user_title' | translate }}
                        </p>
                        <p class="text-sm">
                            {{ 'translate_alert_is_user_text' | translate }}
                        </p>
                    </div>

                    <div *ngIf="!isUser">
                        <p class="font-bold">
                            {{
                                'translate_alert_is_not_user_title' | translate
                            }}
                        </p>
                        <p class="text-sm">
                            {{ 'translate_alert_is_not_user_text' | translate }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <mnm-form
            *ngIf="formState"
            [state]="formState"
            [translateLabels]="true"
        >
            <div class="mt-2 text-center">
                <button
                    type="submit"
                    class="btn-lg btn btn-primary"
                    (click)="submit()"
                    [disabled]="isSubmitting"
                >
                    <app-loading-ring
                        *ngIf="isSubmitting"
                        class="me-2"
                    ></app-loading-ring>
                    <i class="fa-light fa-save me-2"></i>
                    <span>{{ 'translate_save' | translate }}</span>
                </button>
            </div>
        </mnm-form>
    </div>
</app-page>
