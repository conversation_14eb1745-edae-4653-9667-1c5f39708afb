import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { Subject } from 'rxjs';
import { debounceTime, finalize, first, takeUntil } from 'rxjs/operators';
import { Innovator } from '@masar/common/models';
import { MnmFormState } from '@masar/shared/components';
import { InnovatorService } from '../innovator.service';
import { fields } from './fields';
import { HelperService } from '@masar/core/services';

@Component({
    selector: 'app-new',
    templateUrl: './new.component.html',
})
export class NewComponent implements OnInit, OnDestroy {
    public employeeNumber?: string;
    public isUser?: boolean;

    public isSubmitting = false;
    public mode: 'new' | 'edit';
    public formState: MnmFormState;

    private unsubscribeAll = new Subject();

    public constructor(
        private readonly helperService: HelperService,
        private notificationService: NotificationService,
        private innovatorService: InnovatorService,
        private translateService: TranslateService,
        private activatedRoute: ActivatedRoute,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);

        this.monitorForm();
    }

    public ngOnInit(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[0].path) {
                case 'new':
                    this.mode = 'new';

                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.formState.group.controls['employeeNumber'].disable();
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            const id = params.id;
                            this.innovatorService
                                .get(id, true)
                                .subscribe(item => {
                                    this.fillForm(item);
                                });
                        });
                    break;
            }
        });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const observable =
            this.mode === 'new'
                ? this.innovatorService.create(
                      this.formState.group.getRawValue()
                  )
                : this.innovatorService.update(
                      this.formState.group.getRawValue()
                  );

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(innovator => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'innovator'],
                    innovator.id
                );
            });
    }

    private fillForm(item: Innovator): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }

    private monitorForm(): void {
        // Monitor Employee Number
        this.formState.group.controls.employeeNumber.valueChanges
            .pipe(debounceTime(150), takeUntil(this.unsubscribeAll))
            .subscribe((employeeNumber: any) => {
                const controlsNames = ['nameAr', 'nameEn'];
                const controls = controlsNames.map(
                    n => this.formState.group.controls[n]
                );

                if (employeeNumber) {
                    this.innovatorService
                        .checkEmployeeNumber(employeeNumber)
                        .subscribe(res => {
                            this.employeeNumber = employeeNumber;
                            this.isUser = res.isUser;

                            if (!res.isUser) {
                                controls.forEach(c => c.enable());
                                return;
                            }

                            controls[0].setValue(res.nameAr);
                            controls[1].setValue(res.nameEn);
                            controls.forEach(c => c.disable());
                        });
                } else {
                    this.employeeNumber = '';
                    this.isUser = false;
                }
            });
    }
}
