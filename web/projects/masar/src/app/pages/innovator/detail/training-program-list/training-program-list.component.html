<!-- Content-->
<ng-container content>
    <table>
        <thead>
            <tr>
                <th>{{ 'translate_name' | translate }}</th>
                <th>{{ 'translate_training_hours' | translate }}</th>
                <th>{{ 'translate_year' | translate }}</th>
                <th></th>
            </tr>
        </thead>

        <tbody>
            <tr *ngFor="let item of trainingPrograms; let idx = index">
                <!-- Name -->
                <td>{{ item.name }}</td>

                <!-- Hours -->
                <td class="text-center">{{ item.hours }}</td>

                <!-- Year -->
                <td class="text-center">{{ item.year }}</td>

                <!-- Links & Buttons -->
                <td class="whitespace-nowrap">
                    <!-- Delete -->
                    <button
                        class="btn btn-sm btn-info me-2"
                        (click)="edit(item)"
                        [appTooltip]="'translate_delete' | translate"
                    >
                        <i class="fa-light fa-edit"></i>
                    </button>

                    <!-- Delete -->
                    <button
                        class="btn btn-sm btn-danger me-2"
                        [disabled]="currentlyDeleting.includes(item.id)"
                        (confirm)="delete(item)"
                        [appTooltip]="'translate_delete' | translate"
                        [swal]="{
                            title:
                                'translate_delete_this_item_question_mark'
                                | translate,
                            confirmButtonText: 'translate_yes' | translate,
                            cancelButtonText: 'translate_cancel' | translate,
                            showCancelButton: true,
                            showCloseButton: true
                        }"
                    >
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>

            <tr *ngIf="trainingPrograms && !trainingPrograms.length">
                <td colspan="4" class="text-center">
                    <span class="text-gray-500">
                        {{ 'translate_not_found' | translate }}
                    </span>
                </td>
            </tr>
        </tbody>
    </table>
</ng-container>
