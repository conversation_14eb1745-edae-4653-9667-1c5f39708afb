import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { Award } from '@masar/common/models';
import { AwardService } from '../../award.service';

@Component({
    selector: 'app-award-list',
    templateUrl: './award-list.component.html',
})
export class AwardListComponent {
    @Input()
    public awards: Award[];

    @Output()
    public editAward: EventEmitter<Award> = new EventEmitter<Award>();

    @Output()
    public deleteAward: EventEmitter<Award> = new EventEmitter<Award>();

    public currentlyDeleting: string[] = [];

    public constructor(
        private awardService: AwardService,
        private notificationService: NotificationService
    ) {}

    public edit(item: Award): void {
        this.editAward.emit(item);
    }

    public delete(item: Award): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeleting.push(item.id);
        this.awardService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.deleteAward.emit(item);
            });
    }
}
