import { Component, Input } from '@angular/core';
import { functions } from '@masar/common/misc/functions';
import { Innovator } from '@masar/common/models';

@Component({
    selector: 'app-innovator-overview',
    templateUrl: './innovator-overview.component.html',
})
export class InnovatorOverviewComponent {
    @Input()
    public innovator: Innovator;

    public innovatorOverview: {
        label: string;
        propertyName: keyof Innovator;
        icon: string;
    }[] = [
        {
            label: 'translate_innovations',
            propertyName: functions.nameOf<Innovator>('innovationCount'),
            icon: 'fa-lightbulb',
        },
        {
            label: 'translate_awards',
            propertyName: functions.nameOf<Innovator>('awardCount'),
            icon: 'fa-award',
        },
        {
            label: 'translate_training_programs_count',
            propertyName: functions.nameOf<Innovator>('trainingProgramCount'),
            icon: 'fa-graduation-cap',
        },
        {
            label: 'translate_programs_hours',
            propertyName: functions.nameOf<Innovator>('trainingHours'),
            icon: 'fa-chalkboard-user',
        },
    ];
}
