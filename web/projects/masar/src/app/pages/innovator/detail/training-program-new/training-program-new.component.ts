import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { TrainingProgram } from '@masar/common/models';
import { MnmFormState } from '@masar/shared/components';
import { TrainingProgramService } from '../../training-program.service';
import { fields } from './fields';

@Component({
    selector: 'app-training-program-new',
    templateUrl: './training-program-new.component.html',
})
export class TrainingProgramNewComponent implements OnInit {
    @Input()
    public innovatorId: string;

    @Input()
    public item?: TrainingProgram;

    @Output()
    public trainingProgramCreated: EventEmitter<TrainingProgram> =
        new EventEmitter<TrainingProgram>();

    @Output()
    public trainingProgramUpdated: EventEmitter<TrainingProgram> =
        new EventEmitter<TrainingProgram>();

    public isSubmitting = false;
    public formState: MnmFormState;

    public constructor(
        private notificationService: NotificationService,
        private trainingProgramService: TrainingProgramService,
        private translateService: TranslateService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);
    }

    public ngOnInit(): void {
        if (this.item) {
            this.trainingProgramService
                .get(this.item.id, true)
                .subscribe(item => {
                    this.fillForm(item);
                });
        }
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const observable = !this.item
            ? this.trainingProgramService.create(
                  this.innovatorId,
                  this.formState.group.getRawValue()
              )
            : this.trainingProgramService.update(
                  this.formState.group.getRawValue()
              );

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(item => {
                if (!this.item) {
                    this.translateService
                        .get('translate_item_added_successfully')
                        .subscribe(str => {
                            this.notificationService.notifySuccess(str);
                        });
                    this.trainingProgramCreated.emit(item);
                } else {
                    this.translateService
                        .get('translate_item_updated_successfully')
                        .subscribe(str => {
                            this.notificationService.notifySuccess(str);
                        });
                    this.trainingProgramUpdated.emit(item);
                }
            });
    }

    private fillForm(item: TrainingProgram): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }
}
