import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { Award } from '@masar/common/models';
import { MnmFormState } from '@masar/shared/components';
import { AwardService } from '../../award.service';
import { fields } from './fields';

@Component({
    selector: 'app-award-new',
    templateUrl: './award-new.component.html',
})
export class AwardNewComponent implements OnInit {
    @Input()
    public innovatorId: string;

    @Input()
    public item?: Award;

    @Output()
    public awardCreated: EventEmitter<Award> = new EventEmitter<Award>();

    @Output()
    public awardUpdated: EventEmitter<Award> = new EventEmitter<Award>();

    public isSubmitting = false;
    public formState: MnmFormState;

    public constructor(
        private notificationService: NotificationService,
        private awardService: AwardService,
        private translateService: TranslateService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);
    }

    public ngOnInit(): void {
        if (this.item) {
            this.awardService.get(this.item.id, true).subscribe(item => {
                this.fillForm(item);
            });
        }
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const observable = !this.item
            ? this.awardService.create(
                  this.innovatorId,
                  this.formState.group.getRawValue()
              )
            : this.awardService.update(this.formState.group.getRawValue());

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(item => {
                if (!this.item) {
                    this.translateService
                        .get('translate_item_added_successfully')
                        .subscribe(str => {
                            this.notificationService.notifySuccess(str);
                        });
                    this.awardCreated.emit(item);
                } else {
                    this.translateService
                        .get('translate_item_updated_successfully')
                        .subscribe(str => {
                            this.notificationService.notifySuccess(str);
                        });
                    this.awardUpdated.emit(item);
                }
            });
    }

    private fillForm(item: Award): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }
}
