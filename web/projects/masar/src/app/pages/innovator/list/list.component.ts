import { Component, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { TableController } from '@masar/common/misc/table';
import { Item, Innovator } from '@masar/common/models';
import { InnovatorService } from '../innovator.service';

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
})
export class ListComponent implements OnDestroy {
    public tableController: TableController<
        Innovator,
        {
            employeeNumber?: string;
            keyword?: string;
            rank?: string;
            innovationLogo?: string;
        }
    >;

    public innovationLogo: Item[];

    public currentlyDeleting: string[] = [];
    public currentlyTogglingLogo: string[] = [];

    public constructor(
        private innovatorService: InnovatorService,
        private notificationService: NotificationService,
        private translateService: TranslateService
    ) {
        this.tableController = new TableController<
            Innovator,
            {
                employeeNumber?: string;
                keyword?: string;
                rank?: string;
                innovationLogo?: string;
            }
        >(
            filter =>
                innovatorService.list(
                    filter.data.employeeNumber,
                    filter.data.keyword,
                    filter.data.rank,
                    filter.data.innovationLogo,
                    filter.pageNumber,
                    filter.pageSize
                ),
            {
                data: {
                    employeeNumber: '',
                    keyword: '',
                    rank: '',
                    innovationLogo: null,
                },
            }
        );
        this.tableController.start();

        const translateEnableLogo = this.translateService.instant(
            'translate_has_a_logo'
        );
        const translateDisableLogo = this.translateService.instant(
            'translate_does_not_have_a_logo'
        );

        this.innovationLogo = [
            { id: 'true', name: translateEnableLogo },
            { id: 'false', name: translateDisableLogo },
        ];
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public delete(item: Innovator): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeleting.push(item.id);
        this.innovatorService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }

    public toggleLogo(item: Innovator): void {
        // add the id of the item to the being toggled array
        // to disable the toggle button in the list.
        this.currentlyTogglingLogo.push(item.id);
        this.innovatorService
            .toggleLogo(item.id)
            .pipe(
                finalize(() => {
                    // remove the toggled item id from the being toggled
                    // list when the deletion is complete.
                    this.currentlyTogglingLogo =
                        this.currentlyTogglingLogo.filter(x => x !== item.id);
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }
}
