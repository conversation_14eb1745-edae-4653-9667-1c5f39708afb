<app-page pageTitle="{{ 'translate_innovators' | translate }}">
    <!-- Tools -->
    <a
        tools
        [routerLink]="['', 'innovator', 'new']"
        class="btn btn-sm btn-outline-white"
    >
        <i class="fa-light fa-plus me-2"></i>
        <span>{{ 'translate_add_new' | translate }}</span>
    </a>

    <!-- Content-->
    <ng-container content>
        <!-- Filter -->
        <app-filter-result-box>
            <!-- Employee Number -->
            <app-search-input
                [placeholder]="'translate_search_by_employee_number'"
                [(ngModel)]="tableController.filter.data.employeeNumber"
                [tableController]="tableController"
            ></app-search-input>

            <!-- Employee Name -->
            <app-search-input
                [placeholder]="'translate_search_by_innovator_name'"
                [(ngModel)]="tableController.filter.data.keyword"
                [tableController]="tableController"
            ></app-search-input>
            <!-- Rank -->
            <app-search-input
                [placeholder]="'translate_search_by_rank'"
                [(ngModel)]="tableController.filter.data.rank"
                [tableController]="tableController"
            ></app-search-input>

            <!-- Innovator Logo -->
            <ng-select
                [items]="innovationLogo"
                bindValue="id"
                bindLabel="name"
                placeholder="{{ 'translate_innovation_logo' | translate }}"
                [(ngModel)]="tableController.filter.data.innovationLogo"
                (change)="tableController.filter$.next(true)"
            >
            </ng-select>
        </app-filter-result-box>

        <!-- Table -->
        <app-list-loading [items]="tableController.items">
            <table>
                <thead>
                    <tr>
                        <th>{{ 'translate_innovation_logo' | translate }}</th>
                        <th>{{ 'translate_innovator' | translate }}</th>
                        <th>{{ 'translate_rank' | translate }}</th>
                        <th>
                            {{ 'translate_innovations_count' | translate }}
                        </th>
                        <th>
                            {{ 'translate_programs_hours' | translate }}
                        </th>
                        <th>{{ 'translate_activities_count' | translate }}</th>
                        <th>{{ 'translate_awards_count' | translate }}</th>
                        <th style="width: 0%">
                            <i class="fa-light fa-gear"></i>
                        </th>
                    </tr>
                </thead>

                <tbody>
                    <tr
                        *ngFor="
                            let item of tableController.items;
                            let idx = index
                        "
                    >
                        <!-- Innovation Logo -->
                        <td class="text-center">
                            <div
                                *ngIf="item.hasALogo"
                                class="mx-auto h-20 w-20"
                            >
                                <img
                                    src="assets/img/innovator-logo.png"
                                    [alt]="'translate_has_a_logo' | translate"
                                    [title]="'translate_has_a_logo' | translate"
                                    class="mx-auto h-full"
                                />
                            </div>

                            <span
                                *ngIf="!item.hasALogo"
                                class="text-sm text-gray-400"
                            >
                                {{
                                    'translate_does_not_have_a_logo' | translate
                                }}
                            </span>
                        </td>

                        <!-- Name -->
                        <td>
                            <a
                                [routerLink]="[
                                    '',
                                    'innovator',
                                    'detail',
                                    item.id
                                ]"
                            >
                                {{ item.employeeNumber }} - {{ item.name }}
                            </a>
                        </td>

                        <!-- Rank -->
                        <td>{{ item.rank }}</td>

                        <!-- Total Innovations Count -->
                        <td class="text-center">
                            {{ item.innovationCount || 0 }}
                        </td>

                        <!-- Total Programs Hours Count -->
                        <td class="text-center">
                            {{ item.trainingHours || 0 }}
                        </td>

                        <!-- Total Activities Count -->
                        <td class="text-center">
                            {{ item.activityCount || 0 }}
                        </td>

                        <!-- Total Awards Count -->
                        <td class="text-center">
                            {{ item.awardCount || 0 }}
                        </td>

                        <!-- Links & Buttons -->
                        <td>
                            <app-dropdown>
                                <!-- Toggle Logo -->
                                <button
                                    [ngClass]="{
                                        'btn-danger': item.hasALogo,
                                        'hover:btn-danger': item.hasALogo
                                    }"
                                    [disabled]="
                                        currentlyTogglingLogo.includes(item.id)
                                    "
                                    (confirm)="toggleLogo(item)"
                                    [swal]="{
                                        title:
                                            'translate_innovation_toggle_logo'
                                            | translate,
                                        confirmButtonText:
                                            'translate_yes' | translate,
                                        cancelButtonText:
                                            'translate_cancel' | translate,
                                        showCancelButton: true,
                                        showCloseButton: true
                                    }"
                                    class="btn btn-sm btn-success"
                                    [appTooltip]="
                                        (item.hasALogo
                                            ? 'translate_disable_innovation_logo'
                                            : 'translate_enable_innovation_logo'
                                        ) | translate
                                    "
                                >
                                    <i
                                        class="fa-fw"
                                        [ngClass]="{
                                            'fa-solid fa-shield':
                                                !item.hasALogo,
                                            'fa-light fa-shield-slash':
                                                item.hasALogo
                                        }"
                                    ></i>
                                    <!--                                    {{-->
                                    <!--                                        (item.hasALogo-->
                                    <!--                                            ? 'translate_disable_innovation_logo'-->
                                    <!--                                            : 'translate_enable_innovation_logo'-->
                                    <!--                                        ) | translate-->
                                    <!--                                    }}-->
                                </button>

                                <!-- Edit -->
                                <a
                                    [routerLink]="[
                                        '',
                                        'innovator',
                                        'edit',
                                        item.id
                                    ]"
                                    class="btn btn-sm btn-info"
                                    [appTooltip]="'translate_edit' | translate"
                                >
                                    <i class="fa-light fa-edit fa-fw"></i>
                                    <!--                                    {{ 'translate_edit' | translate }}-->
                                </a>

                                <!-- Delete -->
                                <button
                                    [disabled]="
                                        currentlyDeleting.includes(item.id)
                                    "
                                    (confirm)="delete(item)"
                                    [swal]="{
                                        title:
                                            'translate_delete_this_item_question_mark'
                                            | translate,
                                        confirmButtonText:
                                            'translate_yes' | translate,
                                        cancelButtonText:
                                            'translate_cancel' | translate,
                                        showCancelButton: true,
                                        showCloseButton: true
                                    }"
                                    class="btn btn-sm btn-danger"
                                    [appTooltip]="
                                        'translate_delete' | translate
                                    "
                                >
                                    <i class="fas fa-trash fa-fw"></i>
                                    <!--                                    {{ 'translate_delete' | translate }}-->
                                </button>
                            </app-dropdown>
                        </td>
                    </tr>
                </tbody>
            </table>
            <app-table-pagination
                [tableController]="tableController"
            ></app-table-pagination>
        </app-list-loading>
    </ng-container>
</app-page>
