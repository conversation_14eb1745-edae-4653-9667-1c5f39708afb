import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { miscFunctions, Result } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@masar/env/environment';
import { TableResult } from '@masar/common/misc/table';
import { TrainingProgram } from '@masar/common/models';

@Injectable()
export class TrainingProgramService {
    public constructor(private httpClient: HttpClient) {}

    public list(): Observable<TableResult<TrainingProgram>> {
        return this.httpClient
            .get<Result<TableResult<TrainingProgram>>>(
                environment.apiUrl + '/innovator/training-program'
            )
            .pipe(map(result => result.extra));
    }

    public create(
        innovatorId: string,
        trainingProgram: TrainingProgram
    ): Observable<TrainingProgram> {
        return this.httpClient
            .post<Result<TrainingProgram>>(
                environment.apiUrl +
                    '/innovator/training-program/' +
                    innovatorId,
                miscFunctions.objectToURLParams({
                    program: JSON.stringify(trainingProgram),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(
        trainingProgram: TrainingProgram
    ): Observable<TrainingProgram> {
        return this.httpClient
            .put<Result<TrainingProgram>>(
                environment.apiUrl + '/innovator/training-program',
                miscFunctions.objectToURLParams({
                    program: JSON.stringify(trainingProgram),
                })
            )
            .pipe(map(result => result.extra));
    }

    public get(
        id: string,
        forEdit: boolean = false
    ): Observable<TrainingProgram> {
        return this.httpClient
            .get<Result<TrainingProgram>>(
                environment.apiUrl + '/innovator/training-program/' + id,
                { params: new HttpParams().append('forEdit', `${forEdit}`) }
            )
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(
                environment.apiUrl + '/innovator/training-program/' + id
            )
            .pipe(map(result => result.messages[0]));
    }
}
