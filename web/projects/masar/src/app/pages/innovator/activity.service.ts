import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Result, miscFunctions } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@masar/env/environment';
import { TableResult } from '@masar/common/misc/table';
import { Activity } from '@masar/common/models';

@Injectable()
export class ActivityService {
    public constructor(private httpClient: HttpClient) {}

    public list(): Observable<TableResult<Activity>> {
        return this.httpClient
            .get<Result<TableResult<Activity>>>(
                environment.apiUrl + '/innovator/activity'
            )
            .pipe(map(result => result.extra));
    }

    public create(
        innovatorId: string,
        activity: Activity
    ): Observable<Activity> {
        return this.httpClient
            .post<Result<Activity>>(
                environment.apiUrl + '/innovator/activity/' + innovatorId,
                miscFunctions.objectToURLParams({
                    activity: JSON.stringify(activity),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(activity: Activity): Observable<Activity> {
        return this.httpClient
            .put<Result<Activity>>(
                environment.apiUrl + '/innovator/activity',
                miscFunctions.objectToURLParams({
                    activity: JSON.stringify(activity),
                })
            )
            .pipe(map(result => result.extra));
    }

    public get(id: string, forEdit: boolean = false): Observable<Activity> {
        return this.httpClient
            .get<Result<Activity>>(
                environment.apiUrl + '/innovator/activity/' + id,
                {
                    params: new HttpParams().append('forEdit', `${forEdit}`),
                }
            )
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(
                environment.apiUrl + '/innovator/activity/' + id
            )
            .pipe(map(result => result.messages[0]));
    }
}
