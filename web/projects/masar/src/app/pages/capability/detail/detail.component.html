<app-page pageTitle="{{ 'translate_details' | translate }}">
    <ng-container tools>
        <a
            class="btn btn-sm btn-info"
            *ngIf="item"
            [routerLink]="['', 'capability', 'edit', item?.id]"
            ><i class="fa-light fa-edit me-0 md:me-2"></i>
            <span class="hidden md:inline">{{
                'translate_edit' | translate
            }}</span></a
        >

        <a
            class="btn btn-sm btn-white flex items-center gap-1"
            [routerLink]="['', 'capability']"
            ><i class="fas fa-left"></i>
            <span class="hidden md:inline">{{
                'translate_back' | translate
            }}</span></a
        >
    </ng-container>

    <ng-container content>
        <!-- Details -->
        <app-content class="mb-5">
            <table content>
                <tbody>
                    <!-- Name -->
                    <tr>
                        <td>
                            {{ 'translate_capability' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="item">
                                {{ item.name }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Description -->
                    <tr>
                        <td>
                            {{ 'translate_capability_description' | translate }}
                        </td>
                        <td class="whitespace-pre-line">
                            <ng-container *appWaitUntilLoaded="item">
                                {{ item.description }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Type -->
                    <tr>
                        <td>
                            {{ 'translate_capability_type' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="item">
                                {{ item.type.name }}
                            </ng-container>
                        </td>
                    </tr>
                    <!-- Year -->
                    <tr>
                        <td>
                            {{ 'translate_year' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="item">
                                {{ item.year }}
                            </ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>

        <!-- Capability linked kpis -->
        <app-kpi-linker
            class="mb-5"
            [listLinkedKpisCallback]="listLinkedKpis()"
            [listUnlinkedKpisCallback]="listUnlinkedKpis()"
            [linkingCallback]="linkKpi()"
            [unlinkingCallback]="unlinkKpi()"
        ></app-kpi-linker>

        <!-- Capability linked files. -->
        <app-library-file-linker
            [listLinkedFilesCallback]="listLinkedLibraryFiles()"
            [linkingCallback]="linkLibraryFile()"
            [unlinkingCallback]="unlinkLibraryFile()"
        ></app-library-file-linker>
    </ng-container>
</app-page>
