import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NewComponent } from './new/new.component';
import { ListComponent } from './list/list.component';
import { DetailComponent } from './detail/detail.component';
import { CapabilityRoutingModule } from './capability.routing';
import { SharedModule } from '@masar/shared/shared.module';
import { CapabilityComponent } from './capability.component';
import { CapabilityService } from './capability.service';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { NgSelectModule } from '@ng-select/ng-select';
import { MasarModule } from '@masar/features/masar/masar.module';
import { KpiSharedModule } from '@masar/features/kpi-shared/kpi-shared.module';
import { CapabilitySharedModule } from '@masar/features/capability-shared/capability-shared.module';
import { TranslationModule } from '@ng-omar/translation';
import { DashboardComponent } from '@masar/pages/capability/dashboard/dashboard.component';
import { DynamicPageModule } from '@masar/features/dynamic-page/dynamic-page.module';

@NgModule({
    declarations: [
        CapabilityComponent,
        NewComponent,
        ListComponent,
        DetailComponent,
        DashboardComponent,
    ],
    imports: [
        CommonModule,
        CapabilityRoutingModule,
        TranslationModule,
        SharedModule,
        FormsModule,
        ReactiveFormsModule,
        SweetAlert2Module,
        NgSelectModule,
        MasarModule,
        KpiSharedModule,
        CapabilitySharedModule,
        DynamicPageModule,
    ],
    providers: [CapabilityService],
})
export class CapabilityModule {}
