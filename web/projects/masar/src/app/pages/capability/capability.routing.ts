import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListComponent } from './list/list.component';
import { NewComponent } from './new/new.component';
import { DetailComponent } from './detail/detail.component';
import { CapabilityComponent } from './capability.component';
import { DashboardComponent } from '@masar/pages/capability/dashboard/dashboard.component';

const routes: Routes = [
    {
        path: '',
        component: CapabilityComponent,
        children: [
            {
                path: 'dashboard',
                component: DashboardComponent,
                data: {
                    title: 'translate_capabilities_statistics',
                    breadcrumb: [
                        'translate_capabilities',
                        'translate_capabilities_statistics',
                    ],
                },
            },

            {
                path: '',
                component: ListComponent,
                data: {
                    title: 'translate_capabilities',
                    breadcrumb: ['translate_capabilities'],
                },
            },

            {
                path: 'new',
                component: NewComponent,
                data: {
                    title: 'translate_new_capability',
                    breadcrumb: [
                        'translate_capabilities',
                        'translate_new_capability',
                    ],
                },
            },

            {
                path: 'new/:id',
                component: NewComponent,
                data: {
                    title: 'translate_new_capability',
                    breadcrumb: [
                        'translate_capabilities',
                        'translate_new_capability',
                    ],
                },
            },
            {
                path: 'edit/:id',
                component: NewComponent,
                data: {
                    title: 'translate_edit_capability',
                    breadcrumb: ['translate_capabilities', 'translate_edit'],
                },
            },

            {
                path: 'detail/:id',
                component: DetailComponent,
                data: {
                    title: 'translate_capability_details',
                    breadcrumb: ['translate_capabilities', 'translate_details'],
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class CapabilityRoutingModule {}
