import { Injectable } from '@angular/core';
import { Result, miscFunctions } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '@masar/env/environment';
import { Capability, Kpi } from '@masar/common/models';
import { TableResult } from '@masar/common/misc/table';
import { StatisticsGroup } from '@masar/features/dynamic-page/components/statistics-section/interfaces';

@Injectable()
export class CapabilityService {
    public constructor(private httpClient: HttpClient) {}

    public get(id: string, forEdit: boolean = false): Observable<Capability> {
        return this.httpClient
            .get<Result<Capability>>(environment.apiUrl + '/capability/' + id, {
                params: new HttpParams().append('forEdit', `${forEdit}`),
            })
            .pipe(map(result => result.extra));
    }

    public create(capability: Capability): Observable<Capability> {
        return this.httpClient
            .post<Result<Capability>>(
                environment.apiUrl + '/capability',
                miscFunctions.objectToURLParams({
                    capability: JSON.stringify(capability),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(capability: Capability): Observable<Capability> {
        return this.httpClient
            .put<Result<Capability>>(
                environment.apiUrl + '/capability',
                miscFunctions.objectToURLParams({
                    capability: JSON.stringify(capability),
                })
            )
            .pipe(map(result => result.extra));
    }

    // File linking methods.
    public linkLibraryFile(
        id: string,
        libraryFileId: string
    ): Observable<string> {
        return this.httpClient
            .post<Result>(
                `${environment.apiUrl}/capability/library-file/${id}`,
                miscFunctions.objectToURLParams({ libraryFileId })
            )
            .pipe(map(res => res.messages[0]));
    }

    public unlinkLibraryFile(
        id: string,
        libraryFileId: string
    ): Observable<string> {
        return this.httpClient
            .delete<Result>(
                `${environment.apiUrl}/capability/library-file/${id}?libraryFileId=${libraryFileId}`
            )
            .pipe(map(res => res.messages[0]));
    }

    // Kpi unlinking methods.
    public listUnlinkedKpis(
        id: string,
        keyword: string,
        kpiNumber: string,
        pageNumber: number = 0,
        pageSize: number = 20
    ): Observable<TableResult<Kpi>> {
        return this.httpClient
            .get<Result<TableResult<Kpi>>>(
                `${environment.apiUrl}/capability/kpi/${id}/unlinked`,
                {
                    params: new HttpParams()
                        .append('keyword', keyword)
                        .append('kpiNumber', kpiNumber)
                        .append('pageNumber', `${pageNumber}`)
                        .append('pageSize', `${pageSize}`),
                }
            )
            .pipe(map(res => res.extra));
    }

    // Kpi linking methods.
    public linkKpi(id: string, kpiId: string): Observable<string> {
        return this.httpClient
            .post<Result>(
                `${environment.apiUrl}/capability/kpi/${id}`,
                miscFunctions.objectToURLParams({ kpiId })
            )
            .pipe(map(res => res.messages[0]));
    }

    public unlinkKpi(id: string, kpiId: string): Observable<string> {
        return this.httpClient
            .delete<Result>(
                `${environment.apiUrl}/capability/kpi/${id}?kpiId=${kpiId}`
            )
            .pipe(map(res => res.messages[0]));
    }

    public generalStatistics(): Observable<StatisticsGroup[]> {
        return this.httpClient
            .get<Result<StatisticsGroup[]>>(
                `${environment.apiUrl}/capability/statistics`
            )
            .pipe(map(res => res.extra));
    }
}
