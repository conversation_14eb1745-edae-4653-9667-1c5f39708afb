import { MnmFormField } from '@masar/shared/components';
import { Validators } from '@angular/forms';
import { noWhitespaceValidator } from '@masar/common/validators';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },

    {
        fields: [
            {
                name: 'nameAr',
                type: 'text',
                label: 'translate_capability_name_in_arabic',
                size: 3,
                validators: [Validators.required, noWhitespaceValidator],
            },

            {
                name: 'nameEn',
                type: 'text',
                label: 'translate_capability_name_in_english',
                size: 3,
                validators: [noWhitespaceValidator],
            },

            {
                name: 'type',
                type: 'select',
                bindLabel: 'name',
                label: 'translate_type',
                size: 3,
                validators: [Validators.required],
            },
            {
                name: 'year',
                type: 'number',
                label: 'translate_year',
                size: 3,
                validators: [Validators.required],
            },
        ],
    },

    {
        fields: [
            {
                name: 'descriptionAr',
                type: 'textarea',
                label: 'translate_description_in_arabic',
                size: 6,
            },
            {
                name: 'descriptionEn',
                type: 'textarea',
                label: 'translate_description_in_english',
                size: 6,
            },
        ],
    },
];
