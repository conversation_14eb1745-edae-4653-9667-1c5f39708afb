<app-page
    pageTitle="{{
        (mode === 'new'
            ? 'translate_add_new_capability'
            : 'translate_edit_capability'
        ) | translate
    }}"
>
    <!-- Tools -->
    <ng-container tools>
        <!-- Preview -->
        <a
            *ngIf="mode === 'edit'"
            [routerLink]="['', 'capability', 'detail', capabilityId]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-eye me-2"></i>
            <span>{{ 'translate_preview' | translate }}</span>
        </a>

        <!-- New -->
        <a
            *ngIf="mode === 'edit'"
            [routerLink]="['', 'capability', 'new']"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-plus me-2"></i>
            <span>{{ 'translate_add_new' | translate }}</span>
        </a>

        <!-- Back -->
        <a
            [routerLink]="['', 'capability']"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-share me-2"></i>
            <span>{{ 'translate_back' | translate }}</span>
        </a>
    </ng-container>

    <!-- Content -->
    <ng-container content>
        <mnm-form
            *ngIf="formState"
            [state]="formState"
            [translateLabels]="true"
        >
            <div class="mt-2 text-center">
                <button
                    type="submit"
                    class="btn-lg btn btn-primary"
                    (click)="submit()"
                    [disabled]="isSubmitting"
                >
                    <app-loading-ring
                        *ngIf="isSubmitting"
                        class="me-2"
                    ></app-loading-ring>
                    <i class="fa-light fa-save me-2"></i>
                    <span>{{ 'translate_save' | translate }}</span>
                </button>
            </div>
        </mnm-form>
    </ng-container>
</app-page>
