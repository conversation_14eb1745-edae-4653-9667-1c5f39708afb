import { Component, OnInit } from '@angular/core';
import { MnmFormState } from '@masar/shared/components/mnm-form';
import { ActivatedRoute } from '@angular/router';
import { NotificationService } from 'mnm-webapp';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder } from '@angular/forms';
import { HelperService, MiscApiService } from '@masar/core/services';
import { finalize, first } from 'rxjs/operators';
import { CapabilityService } from '../capability.service';
import { Capability } from '@masar/common/models';
import { fields } from './fields';

@Component({
    selector: 'app-new',
    templateUrl: './new.component.html',
})
export class NewComponent implements OnInit {
    public isSubmitting = false;
    public mode: 'new' | 'edit';
    public formState: MnmFormState;

    public capabilityId: string;

    public constructor(
        private readonly helperService: HelperService,
        private notificationService: NotificationService,
        private capabilityService: CapabilityService,
        private translateService: TranslateService,
        private activatedRoute: ActivatedRoute,
        miscApiService: MiscApiService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);

        miscApiService.setMiscItems(this.formState, [
            ['capability-type', 'type', undefined, true],
        ]);
    }

    public ngOnInit(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[0].path) {
                case 'new':
                    this.mode = 'new';
                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            this.capabilityId = params.id;
                            this.capabilityService
                                .get(this.capabilityId, true)
                                .subscribe(item => {
                                    this.fillForm(item);
                                });
                        });
                    break;
            }
        });
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const observable =
            this.mode === 'new'
                ? this.capabilityService.create(
                      this.formState.group.getRawValue()
                  )
                : this.capabilityService.update(
                      this.formState.group.getRawValue()
                  );

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(capability => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'capability'],
                    capability.id
                );
            });
    }

    private fillForm(item: Capability): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }
}
