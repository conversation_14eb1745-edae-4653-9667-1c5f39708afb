<app-page pageTitle="{{ 'translate_manage_capabilities' | translate }}">
    <!-- Tools -->
    <a
        tools
        [routerLink]="['', 'capability', 'new']"
        class="btn btn-sm btn-outline-white"
    >
        <i class="fa-light fa-plus me-2"></i>
        <span>{{ 'translate_add_new' | translate }}</span>
    </a>

    <app-capability-list-full
        content
        [isFilterInUrl]="true"
    ></app-capability-list-full>
</app-page>

<ng-template #deleteButtonTemplate let-item="item">
    <!-- Edit -->
    <a
        [routerLink]="['', 'capability', 'edit', item.id]"
        class="btn btn-sm btn-info"
        [appTooltip]="'translate_edit' | translate"
    >
        <i class="fa-light fa-edit fa-fw"></i>
    </a>

    <!-- Delete -->
    <button
        [disabled]="currentlyProcessing.has(item.id)"
        (confirm)="delete(item)"
        [swal]="{
            title: 'translate_delete_this_item_question_mark' | translate,
            confirmButtonText: 'translate_yes' | translate,
            cancelButtonText: 'translate_cancel' | translate,
            showCancelButton: true,
            showCloseButton: true
        }"
        class="btn btn-sm btn-danger"
        [appTooltip]="'translate_delete' | translate"
    >
        <i class="fas fa-trash fa-fw"></i>
    </button>
</ng-template>
