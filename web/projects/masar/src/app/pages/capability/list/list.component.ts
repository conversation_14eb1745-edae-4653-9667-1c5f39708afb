import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    TemplateRef,
    ViewChild,
} from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Capability } from '@masar/common/models';
import { NotificationService } from 'mnm-webapp';
import { CapabilityListFullComponent } from '@masar/features/capability-shared/components/capability-list-full/capability-list-full.component';
import { SharedCapabilityService } from '@masar/shared/services';

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
})
export class ListComponent implements AfterViewInit {
    @ViewChild(CapabilityListFullComponent)
    private list: CapabilityListFullComponent;

    @ViewChild('deleteButtonTemplate')
    private deleteButtonTemplate: TemplateRef<any>;

    public currentlyProcessing = new Set<string>();

    public constructor(
        private notificationService: NotificationService,
        private capabilityService: SharedCapabilityService,
        private changeDetectorRef: ChangeDetectorRef
    ) {}

    public ngAfterViewInit(): void {
        this.list.list.customFields = [
            {
                name: '',
                template: this.deleteButtonTemplate,
            },
        ];

        this.changeDetectorRef.detectChanges();
    }

    public delete(item: Capability): void {
        this.currentlyProcessing.add(item.id);
        this.capabilityService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    this.currentlyProcessing.delete(item.id);
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.list.refreshItems();
            });
    }
}
