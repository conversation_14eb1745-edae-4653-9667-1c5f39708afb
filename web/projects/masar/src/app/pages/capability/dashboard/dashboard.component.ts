import { Component } from '@angular/core';
import { CapabilityService } from '@masar/pages/capability/capability.service';
import {
    StatisticsGroup,
    StatisticsGroupLinkMap,
} from '@masar/features/dynamic-page/components/statistics-section/interfaces';

@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
})
export class DashboardComponent {
    public statisticsGroups?: StatisticsGroup[];

    public statisticsGroupLinkMap: StatisticsGroupLinkMap = {
        'capability-all': () => ({ url: ['', 'capability'] }),
    };

    public constructor(private readonly capabilityService: CapabilityService) {
        this.capabilityService.generalStatistics().subscribe(data => {
            this.statisticsGroups = data;
        });
    }
}
