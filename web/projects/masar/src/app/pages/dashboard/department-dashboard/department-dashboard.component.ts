import { Component, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { combineLatest, Subject } from 'rxjs';
import { switchMap, takeUntil } from 'rxjs/operators';
import { Department } from '@masar/common/models';
import { YearService } from '@masar/core/services';
import { DashboardService } from '../dashboard.service';

@Component({
    selector: 'app-department-dashboard',
    templateUrl: './department-dashboard.component.html',
})
export class DepartmentDashboardComponent implements OnDestroy {
    public parent: Department;
    public children: Department[];

    private unsubscribeAll = new Subject();
    public constructor(
        dashboardService: DashboardService,
        activatedRoute: ActivatedRoute,
        yearService: YearService
    ) {
        combineLatest([activatedRoute.queryParamMap, yearService.changes$])
            .pipe(takeUntil(this.unsubscribeAll))
            .pipe(
                switchMap(([params]) => {
                    const parentId = params.get('parentId');
                    return dashboardService.departments(parentId);
                })
            )
            .subscribe(data => {
                this.parent = data.parent;
                this.children = data.children;
            });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }
}
