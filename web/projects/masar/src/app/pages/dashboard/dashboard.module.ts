import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MainDashboardComponent } from './main-dashboard/main-dashboard.component';
import { DashboardRoutingModule } from './dashboard.routing';
import { SharedModule } from '@masar/shared/shared.module';
import { NgCircleProgressModule } from 'ng-circle-progress';
import { DashboardService } from './dashboard.service';
import { MasarModule } from '@masar/features/masar/masar.module';
import { DepartmentDashboardItemComponent } from './department-dashboard/components/department-dashboard-item/department-dashboard-item.component';
import { DashboardComponent } from './dashboard.component';
import { FormsModule } from '@angular/forms';
import { GoalGridComponent } from './main-dashboard/components/goal-grid/goal-grid.component';
import { DepartmentDashboardComponent } from './department-dashboard/department-dashboard.component';
import { TranslationModule } from '@ng-omar/translation';
import { TopItemsComponent } from '@masar/pages/dashboard/main-dashboard/dashboards/dashboard-default/top-items/top-items.component';
import { DashboardStyle1Component } from '@masar/pages/dashboard/main-dashboard/dashboards/dashboard-style-1/dashboard-style-1.component';
import { DashboardSettingService } from '@masar/pages/system-settings/setting/dashboard-setting.service';
import { GenerateColorByIndexPipe } from '@masar/pages/dashboard/main-dashboard/dashboards/dashboard-style-1/pipes/generate-color-by-index.pipe';
import { TranslatePropertyPipe } from '@masar/pages/dashboard/main-dashboard/dashboards/dashboard-style-1/pipes/translate-property.pipe';
import { DashboardDefaultComponent } from '@masar/pages/dashboard/main-dashboard/dashboards/dashboard-default/dashboard-default.component';
import { TopItemDialogComponent } from '@masar/pages/dashboard/main-dashboard/components/top-item-dialog/top-item-dialog.component';

@NgModule({
    declarations: [
        DashboardComponent,
        MainDashboardComponent,
        DashboardDefaultComponent,
        DashboardStyle1Component,
        DepartmentDashboardComponent,
        DepartmentDashboardItemComponent,
        GoalGridComponent,
        TopItemsComponent,
        TopItemDialogComponent,
        GenerateColorByIndexPipe,
        TranslatePropertyPipe,
    ],
    imports: [
        CommonModule,
        DashboardRoutingModule,
        TranslationModule,
        SharedModule,
        MasarModule,
        FormsModule,
        NgCircleProgressModule.forRoot({}),
    ],
    providers: [DashboardService, DashboardSettingService],
})
export class DashboardModule {}
