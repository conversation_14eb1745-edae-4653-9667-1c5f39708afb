import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import {
    Capability,
    Department,
    Kpi,
    KpiProgress,
    Pillar,
    Principle,
    Standard,
    SystemStat,
    Tournament,
} from '@masar/common/models';
import { Observable } from 'rxjs';
import { Result } from 'mnm-webapp';
import { environment } from '@masar/env/environment';
import { map } from 'rxjs/operators';
import { TableController } from '@masar/common/misc/table';
import { DashboardTopItemDto } from '@masar/pages/dashboard/main-dashboard/dtos';

@Injectable()
export class DashboardService {
    public constructor(private httpClient: HttpClient) {}

    public topItems(): Observable<DashboardTopItemDto> {
        return this.httpClient
            .get<Result<DashboardTopItemDto>>(
                environment.apiUrl + '/dashboard/top-items'
            )
            .pipe(map(result => result.extra));
    }

    public goals(): Observable<
        {
            id: string;
            name: string;
            achieved: number;
            code: string;
            weight: number;
        }[]
    > {
        return this.httpClient
            .get<
                Result<
                    {
                        id: string;
                        name: string;
                        achieved: number;
                        code: string;
                        weight: number;
                    }[]
                >
            >(environment.apiUrl + '/dashboard/goal')
            .pipe(map(result => result.extra));
    }

    public kpiProgress(): Observable<KpiProgress[]> {
        return this.httpClient
            .get<Result<KpiProgress[]>>(
                environment.apiUrl + '/dashboard/kpi-progress'
            )
            .pipe(map(result => result.extra));
    }

    public departments(parentId: string = ''): Observable<{
        parent: Department;
        children: Department[];
    }> {
        return this.httpClient
            .get<
                Result<{
                    parent: Department;
                    children: Department[];
                }>
            >(environment.apiUrl + '/dashboard/department?parentId=' + parentId)
            .pipe(map(result => result.extra));
    }

    public listTournaments(): Observable<Tournament[]> {
        return this.httpClient
            .get<Result<Tournament[]>>(
                `${environment.apiUrl}/dashboard/excellence/tournament`
            )
            .pipe(map(res => res.extra));
    }

    public getTournament(tournamentId: string): Observable<Tournament> {
        return this.httpClient
            .get<Result<Tournament>>(
                `${environment.apiUrl}/dashboard/excellence/tournament/${tournamentId}`
            )
            .pipe(map(res => res.extra));
    }

    public listPillars({ tournamentId = null } = {}): Observable<Pillar[]> {
        return this.httpClient
            .get<Result<Pillar[]>>(
                `${environment.apiUrl}/dashboard/excellence/pillar`,
                {
                    params: new HttpParams().append(
                        'tournamentId',
                        tournamentId
                    ),
                }
            )
            .pipe(map(res => res.extra));
    }

    public getPillar(pillarId: string): Observable<Pillar> {
        return this.httpClient
            .get<Result<Pillar>>(
                `${environment.apiUrl}/dashboard/excellence/pillar/${pillarId}`
            )
            .pipe(map(res => res.extra));
    }

    public listStandards({
        tournamentId = null,
        pillarId = null,
    } = {}): Observable<Standard[]> {
        return this.httpClient
            .get<Result<Standard[]>>(
                `${environment.apiUrl}/dashboard/excellence/standard`,
                {
                    params: new HttpParams()
                        .append('tournamentId', tournamentId)
                        .append('pillarId', pillarId),
                }
            )
            .pipe(map(res => res.extra));
    }

    public getStandard(standardId: string): Observable<Standard> {
        return this.httpClient
            .get<Result<Standard>>(
                `${environment.apiUrl}/dashboard/excellence/standard/${standardId}`
            )
            .pipe(map(res => res.extra));
    }

    public listPrinciples({
        tournamentId = null,
        pillarId = null,
        standardId = null,
    } = {}): Observable<Principle[]> {
        return this.httpClient
            .get<Result<Principle[]>>(
                `${environment.apiUrl}/dashboard/excellence/principle`,
                {
                    params: new HttpParams()
                        .append('tournamentId', tournamentId)
                        .append('pillarId', pillarId)
                        .append('standardId', standardId),
                }
            )
            .pipe(map(res => res.extra));
    }

    public getPrinciple(principleId: string): Observable<Principle> {
        return this.httpClient
            .get<Result<Principle>>(
                `${environment.apiUrl}/dashboard/excellence/principle/${principleId}`
            )
            .pipe(map(res => res.extra));
    }

    public capabilities(
        {
            tournamentId = null,
            pillarId = null,
            standardId = null,
            principleId = null,
        } = {},
        keyword: string = '',
        pageNumber: number = 0,
        pageSize: number = 20
    ): Observable<TableController<Capability>> {
        return this.httpClient
            .get<Result<TableController<Capability>>>(
                `${environment.apiUrl}/dashboard/excellence/capability`,
                {
                    params: new HttpParams()
                        .append('tournamentId', tournamentId)
                        .append('pillarId', pillarId)
                        .append('standardId', standardId)
                        .append('principleId', principleId)
                        .append('keyword', keyword)
                        .append('pageNumber', pageNumber)
                        .append('pageSize', pageSize),
                }
            )
            .pipe(map(res => res.extra));
    }

    public kpis(
        {
            tournamentId = null,
            pillarId = null,
            standardId = null,
            principleId = null,
        } = {},
        keyword: string = '',
        pageNumber: number = 0,
        pageSize: number = 20
    ): Observable<TableController<Kpi>> {
        return this.httpClient
            .get<Result<TableController<Kpi>>>(
                `${environment.apiUrl}/dashboard/excellence/kpi`,
                {
                    params: new HttpParams()
                        .append('tournamentId', tournamentId)
                        .append('pillarId', pillarId)
                        .append('standardId', standardId)
                        .append('principleId', principleId)
                        .append('keyword', keyword)
                        .append('pageNumber', pageNumber)
                        .append('pageSize', pageSize),
                }
            )
            .pipe(map(res => res.extra));
    }

    public stats(): Observable<SystemStat> {
        return this.httpClient
            .get<Result<SystemStat>>(environment.apiUrl + '/dashboard/stat')
            .pipe(map(result => result.extra));
    }
}
