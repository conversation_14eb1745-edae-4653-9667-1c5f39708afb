<ng-container *appWaitUntilLoaded="setting">
    <!-- Banner -->
    <div
        [style.background-image]="bannerUrl"
        class="relative mb-36 h-[300px] w-full bg-gray-300 bg-cover"
    >
        <!-- Top items -->
        <div
            class="absolute bottom-0 left-1/2 flex -translate-x-1/2 translate-y-1/2 flex-row items-center justify-center gap-5"
        >
            <div
                *ngFor="let item of topItems?.items"
                (click)="topItemDialog.set(item)"
                class="group flex h-40 w-40 cursor-pointer flex-col items-center justify-center gap-2 rounded-2xl bg-primary p-8 text-white opacity-90 transition-colors hover:opacity-80"
            >
                <i
                    class="transition-transform group-hover:scale-75 text-4xl fa {{
                        item.iconClass
                    }}"
                ></i>
                <span>
                    {{ item.label }}
                </span>
            </div>
        </div>
    </div>

    <app-top-item-dialog #topItemDialog></app-top-item-dialog>

    <!-- Goals grid -->
    <app-goal-grid
        class="mb-24 block"
        *appWaitUntilLoaded="goals && goalKpiTypeIds"
        [goals]="goals"
        [goalKpiTypeIds]="goalKpiTypeIds"
        [gaugeStyle]="'style_1'"
        displayStyle="style_1"
    ></app-goal-grid>

    <!-- Sections -->
    <div class="mb-16 flex flex-col gap-16">
        <div
            *ngFor="let section of setting.sections"
            class="flex flex-col items-center gap-2"
        >
            <ng-container *ngIf="!section.isHidden">
                <h2 class="mb-2 text-3xl font-bold text-primary">
                    {{ section | translateProperty }}
                </h2>

                <ng-container [ngSwitch]="section.style">
                    <!-- Style 1  section -->
                    <div
                        *ngSwitchCase="'style_1'"
                        class="grid w-full grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                    >
                        <ng-container
                            *ngFor="let item of section.items; let idx = index"
                        >
                            <a
                                *ngIf="!item.isHidden"
                                [routerLink]="item.link"
                                [ngStyle]="{
                                    backgroundColor: idx | generateColorByIndex
                                }"
                                class="relative flex cursor-pointer flex-col items-center gap-2 px-4 py-16 text-white no-underline after:absolute after:left-0 after:top-0 after:h-full after:w-full after:bg-black after:opacity-0 after:transition-opacity hover:text-white hover:no-underline hover:after:opacity-20"
                            >
                                <app-image
                                    class="h-36 w-36"
                                    *ngIf="item.iconFile"
                                    [imageObservable]="
                                        dashboardSettingService.getStyle1File(
                                            item.iconFile.name
                                        )
                                    "
                                ></app-image>
                                <span>{{ item | translateProperty }}</span>
                            </a>
                        </ng-container>
                    </div>

                    <!-- Style 2  section -->
                    <div
                        *ngSwitchCase="'style_2'"
                        class="grid w-full grid-cols-2 gap-2 md:grid-cols-3 lg:grid-cols-5 lg:gap-4 xl:grid-cols-6 xl:gap-8"
                    >
                        <ng-container
                            *ngFor="let item of section.items; let idx = index"
                        >
                            <a
                                *ngIf="!item.isHidden"
                                [routerLink]="item.link"
                                class="group flex flex-col items-center overflow-hidden rounded-2xl border border-gray-300 bg-primary-100 text-primary no-underline transition-colors hover:bg-primary hover:text-primary hover:no-underline"
                            >
                                <div
                                    [ngStyle]="{
                                        backgroundColor:
                                            idx | generateColorByIndex
                                    }"
                                    class="flex h-3/4 w-full flex-col items-center justify-center py-8"
                                >
                                    <app-image
                                        class="h-32 w-32 transition-transform group-hover:scale-75"
                                        *ngIf="item.iconFile"
                                        [imageObservable]="
                                            dashboardSettingService.getStyle1File(
                                                item.iconFile.name
                                            )
                                        "
                                    ></app-image>
                                </div>

                                <div
                                    class="px-4 py-4 text-center transition-all group-hover:scale-125 group-hover:text-primary-100"
                                >
                                    {{ item | translateProperty }}
                                </div>
                            </a>
                        </ng-container>
                    </div>
                </ng-container>
            </ng-container>
        </div>
    </div>
</ng-container>
