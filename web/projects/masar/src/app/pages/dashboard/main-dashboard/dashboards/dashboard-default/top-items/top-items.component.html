<app-content
    *ngIf="dashboardTopItemDto && dashboardTopItemDto.items?.length"
    [contentTitle]="dashboardTopItemDto.title ?? '' | translate"
>
    <div content class="flex flex-row justify-around">
        <div
            *ngFor="let item of dashboardTopItemDto.items"
            class="flex flex-row items-center"
        >
            <!-- Icon -->
            <button
                (click)="topItemDialog.set(item)"
                class="me-2 flex h-14 w-14 cursor-pointer items-center justify-center rounded-full bg-secondary-400 transition-colors"
            >
                <div
                    class="flex h-9 w-9 items-center justify-center rounded-full bg-secondary-300 text-white"
                >
                    <i class="text-2xl" [ngClass]="item.iconClass"></i>
                </div>
            </button>

            <!-- Text -->
            <span>{{ item.label }}</span>
        </div>
    </div>
</app-content>

<app-top-item-dialog #topItemDialog></app-top-item-dialog>
