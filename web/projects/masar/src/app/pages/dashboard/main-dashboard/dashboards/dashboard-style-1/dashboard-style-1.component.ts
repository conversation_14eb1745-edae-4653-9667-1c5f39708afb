import { Component, OnDestroy } from '@angular/core';
import { DashboardStyle1Setting } from '@masar/common/models';
import { DashboardSettingService } from '@masar/pages/system-settings/setting/dashboard-setting.service';
import { DomSanitizer, SafeStyle } from '@angular/platform-browser';
import { DashboardTopItemDto } from '@masar/pages/dashboard/main-dashboard/dtos';
import { DashboardService } from '@masar/pages/dashboard/dashboard.service';
import { switchMap, takeUntil } from 'rxjs/operators';
import { MiscApiService, YearService } from '@masar/core/services';
import { Subject } from 'rxjs';

@Component({
    selector: 'app-dashboard-style-1',
    templateUrl: 'dashboard-style-1.component.html',
})
export class DashboardStyle1Component implements OnDestroy {
    public setting: DashboardStyle1Setting;
    public bannerUrl: SafeStyle;

    public topItems?: DashboardTopItemDto;

    public goalKpiTypeIds: string[];
    public goals: {
        id: string;
        name: string;
        achieved: number;
    }[];

    private unsubscribeAll = new Subject();

    public constructor(
        public readonly dashboardSettingService: DashboardSettingService,
        private readonly dashboardService: DashboardService,
        private readonly sanitizer: DomSanitizer,
        private readonly yearService: YearService,
        private readonly miscApiService: MiscApiService
    ) {
        this.loadDashboardSetting();
        this.loadDashboardTopItems();
        this.loadKpiTypeIds();
        this.initGoalsLoader();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    private loadDashboardSetting(): void {
        this.dashboardSettingService.getStyle1Setting().subscribe(item => {
            this.setting = item;

            if (this.setting.bannerFile) {
                this.dashboardSettingService
                    .getStyle1File(this.setting.bannerFile.name)
                    .subscribe(blob => {
                        this.bannerUrl =
                            this.sanitizer.bypassSecurityTrustStyle(
                                `url(${URL.createObjectURL(blob)})`
                            );
                    });
            }
        });
    }

    private loadDashboardTopItems(): void {
        this.dashboardService
            .topItems()
            .subscribe(result => (this.topItems = result));
    }

    private loadKpiTypeIds(): void {
        this.miscApiService.kpiTypes().subscribe(items => {
            this.goalKpiTypeIds = items
                .filter(x => x.isUsedToComputeGoalAchievement)
                .map(x => x.id);
        });
    }

    private initGoalsLoader(): void {
        this.yearService.changes$
            .pipe(takeUntil(this.unsubscribeAll))
            .pipe(switchMap(() => this.dashboardService.goals()))
            .subscribe(items => (this.goals = items));
    }
}
