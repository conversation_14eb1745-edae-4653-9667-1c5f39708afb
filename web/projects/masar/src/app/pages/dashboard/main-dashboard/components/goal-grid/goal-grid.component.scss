.card {
    @apply relative h-52 w-full cursor-pointer rounded bg-primary-800 hover:no-underline;

    transform: perspective(100px) rotateY(0) rotateX(0);
    transition: transform 150ms;
}

.card:hover {
    transform: perspective(100px) rotateY(var(--deg-y)) rotateX(var(--deg-x));
}

.card::after,
.card::before {
    @apply absolute left-0 top-0 h-full w-full opacity-0;

    border-radius: inherit;
    content: '';
    transition: opacity 500ms;
}

.card::before {
    z-index: 1;
    background: radial-gradient(
        800px circle at var(--mouse-x) var(--mouse-y),
        rgb(255 255 255 / 30%),
        transparent 40%
    );
}

.card::after {
    z-index: 3;
    background: radial-gradient(
        800px circle at var(--mouse-x) var(--mouse-y),
        rgb(255 255 255 / 10%),
        transparent 40%
    );
}

.card:hover::after,
.cards:hover::before {
    @apply opacity-100;
}

.card-content {
    @apply relative flex flex-col items-center justify-center gap-2 bg-primary-500 font-bold text-white hover:text-white;

    z-index: 2;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    border-radius: inherit;
    margin: 2px;
}
