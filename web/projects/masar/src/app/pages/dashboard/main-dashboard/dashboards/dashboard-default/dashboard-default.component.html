<div class="grid gap-2">
    <!-- Top items -->
    <app-top-items [dashboardTopItemDto]="dashboardTopItemsDto"></app-top-items>

    <!-- Goals grid -->
    <app-goal-grid
        *appWaitUntilLoaded="goals && goalKpiTypeIds"
        [goals]="goals"
        [goalKpiTypeIds]="goalKpiTypeIds"
    ></app-goal-grid>

    <!-- Kpi overview -->
    <app-content [contentTitle]="'translate_kpis_overview' | translate">
        <div content class="text-center text-xl font-bold">
            <div
                *appWaitUntilLoaded="kpiAchievementStats && dashboardKpiTypeIds"
                [@verticalFlippingCardGrid]="kpiAchievementStats.length"
                class="grid grid-cols-1 grid-rows-5 gap-2 md:grid-cols-5 md:grid-rows-1"
            >
                <!-- Tile-->
                <a
                    [routerLink]="['', 'kpi']"
                    [queryParams]="{
                        progresses: item.id,
                        typeIds: dashboardKpiTypeIds
                    }"
                    *ngFor="let item of kpiAchievementStats"
                    [@verticalFlippingCard]
                    class="relative flex flex-col justify-center rounded p-4 font-bold text-white no-underline transition-colors hover:text-white hover:no-underline"
                    [ngClass]="{
                        'bg-red-600 hover:bg-red-700': item.id === 'late',
                        'bg-yellow-600 hover:bg-yellow-700':
                            item.id === 'close',
                        'bg-green-600 hover:bg-green-700':
                            item.id === 'achieved',
                        'bg-blue-600 hover:bg-blue-700': item.id === 'exceeded',
                        'bg-gray-600 hover:bg-gray-700': item.id === 'not_yet'
                    }"
                >
                    <!-- Background icon -->
                    <em
                        class="fa-light fa-chart-line absolute bottom-3 end-3 text-6xl opacity-20"
                    ></em>

                    <!-- Count -->
                    <div class="mb-2 text-2xl">{{ item.count }}</div>

                    <!-- Title -->
                    <div class="text-lg">
                        {{ item.name }}
                    </div>
                </a>
            </div>
        </div>
    </app-content>

    <!-- Stats -->
    <app-content contentTitle="{{ 'translate_system_stats' | translate }}">
        <div
            *appWaitUntilLoaded="stats"
            [@flippingCardGrid]="statsArray.length"
            content
            class="grid grid-cols-2 grid-rows-3 gap-2"
        >
            <app-permission-link
                *ngFor="let item of statsArray"
                [@flippingCard]
                [permissionId]="item.permissionId"
                [noUnderline]="true"
                [link]="item.link"
                class="block"
            >
                <div
                    [ngStyle]="{ backgroundColor: generateColor(item.title) }"
                    class="relative flex flex-col gap-3 rounded px-3 py-7 font-bold text-white transition-opacity hover:opacity-90"
                >
                    <!-- Card title -->
                    <span>{{ item.title | translate }}</span>

                    <!-- Count -->
                    <span class="text-center text-3xl">
                        {{ stats[item.property] }}
                    </span>

                    <!-- Background icon -->
                    <i
                        class="absolute text-8xl end-2 bottom-2 text-white text-opacity-25 {{
                            item.icon
                        }}"
                    ></i>
                </div>
            </app-permission-link>
        </div>
    </app-content>
</div>
