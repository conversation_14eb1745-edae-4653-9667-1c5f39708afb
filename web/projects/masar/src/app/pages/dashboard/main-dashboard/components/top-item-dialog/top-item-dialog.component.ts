import { Component } from '@angular/core';
import { DashboardTopItem } from '@masar/pages/dashboard/main-dashboard/interfaces';
import { flippingCard } from '@masar/common/animations';
import { animations } from './animations';

@Component({
    selector: 'app-top-item-dialog',
    templateUrl: 'top-item-dialog.component.html',
    animations: [...flippingCard, ...animations],
})
export class TopItemDialogComponent {
    public selection?: DashboardTopItem;

    public set(item: DashboardTopItem): void {
        this.selection = item;
    }

    public clear(): void {
        this.selection = undefined;
    }
}
