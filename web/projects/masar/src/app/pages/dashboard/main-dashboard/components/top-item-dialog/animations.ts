import {
    animate,
    animateChild,
    group,
    query,
    style,
    transition,
    trigger,
} from '@angular/animations';

export const animations = [
    trigger('selectedTopItemOverlay', [
        transition(':enter', [
            group([
                style({ opacity: 0.0 }),
                animate('100ms ease-in', style({ opacity: 1.0 })),
                query('@selectedTopItemDialog', animateChild()),
            ]),
        ]),

        transition(':leave', [
            animate('100ms ease-in', style({ opacity: 0.0 })),
        ]),
    ]),

    trigger('selectedTopItemDialog', [
        transition(':enter', [
            style({
                transform: 'perspective(30cm) rotateX(45deg) translateY(-100%)',
            }),
            animate(
                '350ms cubic-bezier(.37,1.52,.73,.94)',
                style({
                    transform: 'perspective(30cm) rotateX(0) translateY(0)',
                })
            ),
        ]),
    ]),
];
