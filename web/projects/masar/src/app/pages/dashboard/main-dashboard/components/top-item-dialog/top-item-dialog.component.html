<div
    *ngIf="selection"
    [@selectedTopItemOverlay]
    (mouseup)="clear()"
    class="fixed start-0 top-0 z-30 h-full w-full bg-black bg-opacity-20"
>
    <!-- Dialog-->
    <div
        [@selectedTopItemDialog]
        (mouseup)="$event.stopPropagation()"
        class="relative top-16 mx-auto w-10/12 rounded bg-black bg-opacity-70 p-5 text-white md:w-5/12"
    >
        <!-- Title -->
        <h1 class="mb-5 text-xl">{{ selection.label }}</h1>

        <!-- Description -->
        <p *ngIf="selection.description" class="text-lg">
            {{ selection.description }}
        </p>

        <!-- Items -->
        <ng-container *ngIf="selection.tags">
            <span
                *ngFor="let item of selection.tags"
                class="m-2 inline-block rounded bg-secondary-500 px-4 py-2 text-white"
            >
                {{ item }}
            </span>
        </ng-container>
    </div>
</div>
