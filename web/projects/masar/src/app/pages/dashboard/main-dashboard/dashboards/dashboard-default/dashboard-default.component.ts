import { Component, OnDestroy } from '@angular/core';
import { Department, SystemStat } from '@masar/common/models';
import { Subject } from 'rxjs';
import { switchMap, takeUntil } from 'rxjs/operators';
import { DashboardService } from '../../../dashboard.service';
import { flippingCard, verticalFlippingCard } from '@masar/common/animations';
import { functions } from '@masar/common/misc/functions';
import { permissionList } from '@masar/common/constants';
import { MiscApiService, YearService } from '@masar/core/services';
import { DashboardTopItemDto } from '@masar/pages/dashboard/main-dashboard/dtos';

@Component({
    selector: 'app-dashboard-default',
    templateUrl: './dashboard-default.component.html',
    styleUrls: ['./dashboard-default.component.scss'],
    animations: [...flippingCard, ...verticalFlippingCard],
})
export class DashboardDefaultComponent implements OnDestroy {
    public dashboardKpiTypeIds: string[];
    public goalKpiTypeIds: string[];

    public departments: Department[];

    public kpiAchievementStats: any[];

    public dashboardTopItemsDto?: DashboardTopItemDto;

    public goals: {
        id: string;
        name: string;
        achieved: number;
        code: string;
        weight: number;
    }[];

    public stats: SystemStat;

    public statsArray = [
        {
            property: functions.nameOf<SystemStat>('kpiCount'),
            title: 'translate_kpis',
            icon: 'fa-duotone fa-chart-line',
            permissionId: permissionList.kpiRead,
            link: ['', 'kpi'],
        },

        {
            property: functions.nameOf<SystemStat>('libraryFileCount'),
            title: 'translate_library_files',
            icon: 'fa-duotone fa-box-archive',
            link: ['', 'library'],
        },

        {
            property: functions.nameOf<SystemStat>('operationCount'),
            title: 'translate_operations',
            icon: 'fa-duotone fa-gears',
            permissionId: permissionList.operationRead,
            link: ['', 'operation'],
        },

        {
            property: functions.nameOf<SystemStat>('userCount'),
            title: 'translate_users',
            icon: 'fa-duotone fa-users',
            permissionId: permissionList.userRead,
            link: ['', 'user'],
        },

        {
            property: functions.nameOf<SystemStat>('planCount'),
            title: 'translate_operational_plans',
            icon: 'fa-duotone fa-layer-group',
            permissionId: permissionList.planRead,
            link: ['', 'plan'],
        },

        {
            property: functions.nameOf<SystemStat>('taskCount'),
            title: 'translate_plans_tasks',
            icon: 'fa-duotone fa-list-check',
            permissionId: permissionList.planRead,
            link: ['', 'plan-task'],
        },
    ];

    private unsubscribeAll = new Subject();

    public constructor(
        private dashboardService: DashboardService,
        private yearService: YearService,
        private miscApiService: MiscApiService
    ) {
        this.getTopItems();
        this.miscApiService.kpiTypes().subscribe(items => {
            this.dashboardKpiTypeIds = items
                .filter(x => x.isConsideredInDashboard)
                .map(x => x.id);
            this.goalKpiTypeIds = items
                .filter(x => x.isUsedToComputeGoalAchievement)
                .map(x => x.id);
        });

        this.initGoalsLoader();
        this.initKpiAchievementsLoader();
        this.initDepartmentsLoader();

        dashboardService.stats().subscribe(item => (this.stats = item));
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public generateColor(str: string): string {
        // TODO: Adjust base color values below based on theme
        const baseRed = 128;
        const baseGreen = 128;
        const baseBlue = 128;

        // Lazy seeded random hack to get values from 0 to 256
        // for seed just take bitwise XOR of first two chars
        let seed = 0;

        for (let i = 0; i < str.length; i++) {
            seed = str.charCodeAt(i) + ((seed << 5) - seed);
        }

        let rand1 = Math.abs(Math.sin(seed++) * 10000) % 256;
        let rand2 = Math.abs(Math.sin(seed++) * 10000) % 256;
        let rand3 = Math.abs(Math.sin(seed++) * 10000) % 256;

        // Build color
        let red = Math.round((rand1 + baseRed) / 2).toString(16);
        let green = Math.round((rand2 + baseGreen) / 2).toString(16);
        let blue = Math.round((rand3 + baseBlue) / 2).toString(16);

        return `#${red}${green}${blue}`;
    }

    private initGoalsLoader(): void {
        this.yearService.changes$
            .pipe(takeUntil(this.unsubscribeAll))
            .pipe(switchMap(() => this.dashboardService.goals()))
            .subscribe(items => (this.goals = items));
    }

    private initKpiAchievementsLoader(): void {
        this.yearService.changes$
            .pipe(takeUntil(this.unsubscribeAll))
            .pipe(switchMap(() => this.dashboardService.kpiProgress()))
            .subscribe(items => (this.kpiAchievementStats = items));
    }

    private initDepartmentsLoader(): void {
        this.yearService.changes$
            .pipe(takeUntil(this.unsubscribeAll))
            .pipe(switchMap(() => this.dashboardService.departments()))
            .subscribe(data => (this.departments = data.children));
    }

    private getTopItems(): void {
        this.dashboardService
            .topItems()
            .subscribe(result => (this.dashboardTopItemsDto = result));
    }
}
