import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
    name: 'translateProperty',
})
export class TranslatePropertyPipe implements PipeTransform {
    public constructor(private readonly translateService: TranslateService) {}

    public transform(
        obj: { [key: string]: unknown },
        property: string = 'name'
    ): string | { [key: string]: unknown } {
        let lang = this.translateService.currentLang;

        // Capitalize the lang; e.g., ar => Ar
        lang = lang.charAt(0).toUpperCase() + lang.slice(1).toLowerCase();

        property = `${property}${lang}`;

        return property in obj ? (obj[property] as string) : obj;
    }
}
