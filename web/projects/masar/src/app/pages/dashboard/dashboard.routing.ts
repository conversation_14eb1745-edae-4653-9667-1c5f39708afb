import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MainDashboardComponent } from './main-dashboard/main-dashboard.component';
import { DashboardComponent } from './dashboard.component';
import { DepartmentDashboardComponent } from './department-dashboard/department-dashboard.component';

const routes: Routes = [
    {
        path: '',
        component: DashboardComponent,
        children: [
            {
                path: '',
                component: MainDashboardComponent,
                data: {
                    title: 'translate_home',
                },
            },
            {
                path: 'department',
                component: DepartmentDashboardComponent,
                data: {
                    title: 'translate_departments_achievement',
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class DashboardRoutingModule {}
