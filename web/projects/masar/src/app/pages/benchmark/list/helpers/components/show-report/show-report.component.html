<app-content>
    <div content class="flex flex-col gap-5">
        <!-- Basic -->
        <table>
            <tbody>
                <tr>
                    <td>{{ 'translate_report_time' | translate }}</td>
                    <td>{{ benchmark.reportTime | date : 'yyyy-MM-dd' }}</td>
                </tr>
                <tr>
                    <td>{{ 'translate_report_type' | translate }}</td>
                    <td>
                        {{
                            benchmark.reportType
                                | translateItem : 'benchmark-report-type'
                                | async
                        }}
                    </td>
                </tr>
                <tr>
                    <td>{{ 'translate_report_details' | translate }}</td>
                    <td style="white-space: pre-line">
                        {{ benchmark.reportDetails }}
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Attachments -->
        <table>
            <thead>
                <tr>
                    <th>{{ 'translate_file' | translate }}</th>
                    <th>{{ 'translate_description' | translate }}</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of benchmark.libraryFiles">
                    <td>
                        <app-library-file-name
                            [libraryFile]="item.libraryFile"
                        ></app-library-file-name>
                    </td>
                    <td>{{ item.description }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</app-content>
