<app-page pageTitle="{{ 'translate_benchmarks' | translate }}">
    <!-- Tools -->
    <a
        *appHasPermissionId="permissionList.benchmarkWrite"
        tools
        [routerLink]="['', 'benchmark', 'new']"
        class="btn btn-sm btn-outline-white"
    >
        <i class="fa-light fa-plus me-2"></i>
        <span>{{ 'translate_add_new' | translate }}</span>
    </a>

    <!-- Content -->
    <ng-container content>
        <!-- Filter box-->
        <app-filter-result-box>
            <!-- Visit Years -->
            <ng-select
                [items]="visitYears"
                bindLabel="name"
                bindValue="id"
                [multiple]="true"
                [(ngModel)]="tableController.filter.data.years"
                (change)="tableController.filter$.next(true)"
                [placeholder]="'translate_visit_year' | translate"
            ></ng-select>

            <!-- Visit date -->
            <div class="flex flex-row items-center gap-2">
                <input
                    #visitDate="appFlatpickr"
                    appFlatpickr
                    [config]="{ mode: 'range' }"
                    type="date"
                    class="flex-grow"
                    [(ngModel)]="tableController.filter.data.visitDateFromTo"
                    (flatPickrChange)="tableController.filter$.next(true)"
                    placeholder="{{ 'translate_visit_date' | translate }}"
                />
                <button
                    (click)="visitDate.clear()"
                    class="btn btn-sm btn-primary"
                >
                    <i class="fa-light fa-times"></i>
                </button>
            </div>

            <!-- Department -->
            <ng-select
                [items]="departments"
                bindLabel="name"
                bindValue="id"
                [multiple]="true"
                [(ngModel)]="tableController.filter.data.departmentIds"
                (change)="tableController.filter$.next(true)"
                placeholder="{{ 'translate_department' | translate }}"
            ></ng-select>

            <!-- include Sub Departments -->
            <div *ngIf="tableController.filter.data.departmentIds?.length">
                <label class="flex flex-row items-center gap-2">
                    <input
                        type="checkbox"
                        [(ngModel)]="
                            tableController.filter.data.includeChildDepartments
                        "
                        (change)="tableController.filter$.next(true)"
                    />
                    <span>
                        {{ 'translate_include_child_departments' | translate }}
                    </span>
                </label>
            </div>

            <!-- Benchmark methods -->
            <ng-select
                [items]="methods"
                bindLabel="name"
                bindValue="id"
                [multiple]="true"
                [(ngModel)]="tableController.filter.data.methods"
                (change)="tableController.filter$.next(true)"
                placeholder="{{
                    'translate_benchmark_methodology' | translate
                }}"
            ></ng-select>

            <!-- Strategic goals -->
            <ng-select
                [items]="goals"
                bindLabel="name"
                bindValue="id"
                [multiple]="true"
                [(ngModel)]="tableController.filter.data.goalIds"
                (change)="tableController.filter$.next(true)"
                placeholder="{{ 'translate_strategic_goals' | translate }}"
                groupBy="yearRange"
            ></ng-select>

            <!-- Management type -->
            <ng-select
                [items]="managementTypes"
                bindLabel="name"
                bindValue="id"
                [multiple]="true"
                [(ngModel)]="tableController.filter.data.managementTypes"
                (change)="tableController.filter$.next(true)"
                placeholder="{{ 'translate_management_type' | translate }}"
            ></ng-select>

            <!-- Request reason -->
            <ng-select
                [items]="requestReasons"
                bindLabel="name"
                bindValue="id"
                [multiple]="true"
                [(ngModel)]="tableController.filter.data.requestReasonIds"
                (change)="tableController.filter$.next(true)"
                placeholder="{{
                    'translate_reasons_for_requesting_benchmark' | translate
                }}"
            ></ng-select>

            <!-- Operation -->
            <ng-select
                [items]="operations"
                bindLabel="name"
                bindValue="id"
                [multiple]="true"
                [(ngModel)]="tableController.filter.data.operationIds"
                (change)="tableController.filter$.next(true)"
                placeholder="{{ 'translate_operations' | translate }}"
            ></ng-select>

            <!-- Kpi keyword -->
            <input
                placeholder="{{ 'translate_search_by_kpi_name' | translate }}"
                [(ngModel)]="tableController.filter.data.kpiKeyword"
                (keyup)="tableController.filter$.next(true)"
            />

            <!-- Entity keyword -->
            <input
                placeholder="{{
                    'translate_search_by_entity_name' | translate
                }}"
                [(ngModel)]="tableController.filter.data.entityNameKeyword"
                (keyup)="tableController.filter$.next(true)"
            />

            <!-- Agenda keyword -->
            <input
                placeholder="{{ 'translate_search_by_agenda' | translate }}"
                [(ngModel)]="tableController.filter.data.agendaKeyword"
                (keyup)="tableController.filter$.next(true)"
            />

            <!-- Initiative status -->
            <ng-select
                [items]="[
                    {
                        id: 'none',
                        name: ('translate_not_approved' | translate)
                    },

                    {
                        id: 'first',
                        name: ('translate_first_approval' | translate)
                    },

                    {
                        id: 'second',
                        name: ('translate_second_approval' | translate)
                    }
                ]"
                bindValue="id"
                bindLabel="name"
                placeholder="{{ 'translate_approval_status' | translate }}"
                [multiple]="true"
                [(ngModel)]="tableController.filter.data.approvalStatuses"
                (change)="tableController.filter$.next(true)"
            >
            </ng-select>
        </app-filter-result-box>

        <!-- Table -->
        <app-list-loading [items]="tableController.items">
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>{{ 'translate_visit_date' | translate }}</th>
                            <th>
                                {{ 'translate_details' | translate }}
                            </th>
                            <th>
                                {{ 'translate_target_entity' | translate }}
                            </th>
                            <th>{{ 'translate_department' | translate }}</th>
                            <th>
                                {{
                                    'translate_reasons_for_requesting_benchmark'
                                        | translate
                                }}
                            </th>
                            <th>
                                {{ 'translate_manager_approval' | translate }}
                            </th>
                            <th>
                                {{
                                    'translate_benchmark_management_approval'
                                        | translate
                                }}
                            </th>
                            <th>
                                {{ 'translate_benchmark_report' | translate }}
                            </th>
                            <th>
                                {{ 'translate_opportunities' | translate }}
                            </th>
                            <th
                                *appHasAnyPermissionId="[
                                    permissionList.benchmarkWrite,
                                    permissionList.benchmarkDelete
                                ]"
                                style="width: 0"
                            >
                                <i class="fa-light fa-gear"></i>
                            </th>
                        </tr>
                    </thead>

                    <tbody>
                        <ng-container
                            *ngFor="
                                let item of tableController.items;
                                let idx = index
                            "
                        >
                            <tr>
                                <!-- Visit date -->
                                <td class="whitespace-nowrap">
                                    <a
                                        [routerLink]="[
                                            '',
                                            'benchmark',
                                            'detail',
                                            item.id
                                        ]"
                                    >
                                        {{
                                            item.visitDate | date : 'yyyy-MM-dd'
                                        }}
                                    </a>
                                </td>

                                <!-- Benchmark method & Benchmark type -->
                                <td>
                                    <div>
                                        <span
                                            class="ng-star-inserted badge badge-info mx-1"
                                        >
                                            {{
                                                item.type
                                                    | translateItem
                                                        : 'benchmark-type'
                                                    | async
                                            }}
                                        </span>
                                    </div>
                                    <div
                                        class="ng-star-inserted badge badge-success mx-1"
                                    >
                                        {{
                                            item.method
                                                | translateItem
                                                    : 'benchmark-method'
                                                | async
                                        }}
                                    </div>
                                </td>

                                <!-- Department -->
                                <td>
                                    {{ item.partner?.name ?? item.entityName }}
                                </td>

                                <!-- Entity -->
                                <td>
                                    {{ item.department.name }}
                                </td>

                                <!-- Request reason -->
                                <td>
                                    <div
                                        *ngFor="
                                            let subItem of item.requestReasons
                                        "
                                        class="badge badge-info mx-1"
                                    >
                                        {{ subItem.name }}
                                    </div>
                                    <div
                                        *ngIf="item.otherRequestReasons"
                                        class="badge badge-info mx-1"
                                    >
                                        {{ item.otherRequestReasons }}
                                    </div>
                                </td>

                                <!-- First approval -->
                                <td>
                                    <ng-container
                                        [ngTemplateOutlet]="
                                            approvalColumnTemplate
                                        "
                                        [ngTemplateOutletContext]="{
                                            item,
                                            approvalUser:
                                                item.firstApprovalUser,
                                            approvalTime:
                                                item.firstApprovalTime,
                                            canBeApproved:
                                                item.canBeFirstApproved,
                                            type: 'first_approve'
                                        }"
                                    ></ng-container>
                                </td>

                                <!-- Second approval -->
                                <td>
                                    <ng-container
                                        [ngTemplateOutlet]="
                                            approvalColumnTemplate
                                        "
                                        [ngTemplateOutletContext]="{
                                            item,
                                            approvalUser:
                                                item.secondApprovalUser,
                                            approvalTime:
                                                item.secondApprovalTime,
                                            canBeApproved:
                                                item.canBeSecondApproved,
                                            type: 'second_approve'
                                        }"
                                    ></ng-container>
                                </td>

                                <!-- Benchmark report -->
                                <td>
                                    <ng-container
                                        *ngIf="
                                            item.reportTime;
                                            else noReportTemplate
                                        "
                                    >
                                        <button
                                            class="btn btn-sm btn-primary"
                                            (click)="showReport(item)"
                                        >
                                            {{
                                                'translate_show_report'
                                                    | translate
                                            }}
                                        </button>
                                    </ng-container>

                                    <ng-template #noReportTemplate>
                                        <ng-container
                                            *ngIf="
                                                item.canHaveReport;
                                                else canNotHaveReportTemplate
                                            "
                                        >
                                            <button
                                                *appHasPermissionId="
                                                    permissionList.benchmarkWrite;
                                                    else canNotHaveReportTemplate
                                                "
                                                (click)="
                                                    showSubmitReportDialog(item)
                                                "
                                                class="btn btn-sm btn-primary flex flex-row items-center gap-2"
                                            >
                                                <i class="fa-light fa-plus"></i>
                                                <span>
                                                    {{
                                                        'translate_add'
                                                            | translate
                                                    }}
                                                </span>
                                            </button>
                                        </ng-container>
                                    </ng-template>

                                    <ng-template #canNotHaveReportTemplate>
                                        {{
                                            'translate_no_report_yet'
                                                | translate
                                        }}
                                    </ng-template>
                                </td>

                                <!-- opportunity count -->
                                <td class="text-center">
                                    {{ item.opportunityCount }}
                                </td>

                                <!-- Controls-->
                                <td
                                    *appHasAnyPermissionId="[
                                        permissionList.benchmarkWrite,
                                        permissionList.benchmarkDelete
                                    ]"
                                >
                                    <app-dropdown>
                                        <a
                                            *appHasPermissionId="
                                                permissionList.benchmarkWrite
                                            "
                                            [routerLink]="[
                                                '',
                                                'benchmark',
                                                'edit',
                                                item.id
                                            ]"
                                            class="btn btn-sm btn-info"
                                            [appTooltip]="
                                                'translate_edit' | translate
                                            "
                                        >
                                            <i
                                                class="fa-light fa-edit fa-fw"
                                            ></i>
                                            <!--                                            {{ 'translate_edit' | translate }}-->
                                        </a>

                                        <button
                                            *appHasPermissionId="
                                                permissionList.benchmarkDelete
                                            "
                                            [disabled]="
                                                currentlyProcessing.includes(
                                                    item.id
                                                )
                                            "
                                            (confirm)="process(item)"
                                            [swal]="{
                                                title:
                                                    'translate_delete_this_item_question_mark'
                                                    | translate,
                                                confirmButtonText:
                                                    'translate_yes' | translate,
                                                cancelButtonText:
                                                    'translate_cancel'
                                                    | translate,
                                                showCancelButton: true,
                                                showCloseButton: true
                                            }"
                                            class="btn btn-sm btn-danger"
                                            [appTooltip]="
                                                'translate_delete' | translate
                                            "
                                        >
                                            <i class="fas fa-trash fa-fw"></i>
                                            <!--                                            {{ 'translate_delete' | translate }}-->
                                        </button>
                                    </app-dropdown>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
            <app-table-pagination
                [tableController]="tableController"
            ></app-table-pagination>
        </app-list-loading>
    </ng-container>
</app-page>

<ng-template
    #approvalColumnTemplate
    let-item="item"
    let-approvalUser="approvalUser"
    let-approvalTime="approvalTime"
    let-canBeApproved="canBeApproved"
    let-type="type"
>
    <ng-container *ngIf="approvalUser; else notApprovedTemplate">
        {{
            'translate_approved_by_name_at_time'
                | translate
                    : {
                          name: approvalUser.name,
                          time: (approvalTime | date : 'yyyy-MM-dd hh:mm a')
                      }
        }}
    </ng-container>

    <ng-template #notApprovedTemplate>
        <ng-container *ngIf="canBeApproved; else canNotBeApprovedTemplate">
            <button
                class="btn btn-sm btn-primary"
                [disabled]="currentlyProcessing.includes(item.id)"
                (click)="process(item, type)"
            >
                {{ 'translate_approve' | translate }}
            </button>
        </ng-container>
    </ng-template>

    <ng-template #canNotBeApprovedTemplate>
        {{ 'translate_not_approved_yet' | translate }}
    </ng-template>
</ng-template>
