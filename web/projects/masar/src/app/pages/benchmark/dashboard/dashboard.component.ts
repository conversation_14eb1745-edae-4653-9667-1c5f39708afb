import { Component } from '@angular/core';
import { BenchmarkService } from '@masar/pages/benchmark/benchmark.service';
import {
    StatisticsGroup,
    StatisticsGroupLinkMap,
} from '@masar/features/dynamic-page/components/statistics-section/interfaces';

@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
})
export class DashboardComponent {
    public statisticsGroups?: StatisticsGroup[];

    public statisticsGroupLinkMap: StatisticsGroupLinkMap = {
        'benchmark-all': () => ({ url: ['', 'benchmark'] }),
        'benchmark-first-approval': () => ({
            url: ['', 'benchmark'],
            queryParams: {
                filter: JSON.stringify({
                    data: { approvalStatuses: ['first'] },
                }),
            },
        }),
        'benchmark-second-approval': () => ({
            url: ['', 'benchmark'],
            queryParams: {
                filter: JSON.stringify({
                    data: { approvalStatuses: ['second'] },
                }),
            },
        }),
        'benchmark-strategic-goal': item => ({
            url: ['', 'benchmark'],
            queryParams: {
                filter: JSON.stringify({
                    data: { goalIds: [item.id] },
                }),
            },
        }),
    };

    public constructor(private readonly benchmarkService: BenchmarkService) {
        this.benchmarkService.generalStatistics().subscribe(data => {
            this.statisticsGroups = data;
        });
    }
}
