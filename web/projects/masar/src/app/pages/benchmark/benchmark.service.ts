import { Injectable } from '@angular/core';
import { Result, miscFunctions } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient, HttpParams } from '@angular/common/http';
import { TableResult } from '@masar/common/misc/table';
import { environment } from '@masar/env/environment';
import { Benchmark } from '@masar/common/models';
import { StatisticsGroup } from '@masar/features/dynamic-page/components/statistics-section/interfaces';

@Injectable()
export class BenchmarkService {
    public constructor(private httpClient: HttpClient) {}

    public list(
        visitDateFrom: Date,
        visitDateTo: Date,
        departmentIds: string[],
        includeChildDepartments: boolean,
        methods: string[],
        goalIds: string[],
        managementTypes: string[],
        requestReasonIds: string[],
        operationIds: string[],
        kpiKeyword: string,
        entityNameKeyword: string,
        agendaKeyword: string,
        approvalStatuses: string[],
        years: number[],
        pageNumber: number,
        pageSize: number = 20
    ): Observable<TableResult<Benchmark>> {
        let params = new HttpParams();

        params = params
            .append('kpiKeyword', kpiKeyword)
            .append('entityNameKeyword', entityNameKeyword)
            .append('agendaKeyword', agendaKeyword)
            .append('visitDateFrom', visitDateFrom?.toISOString())
            .append('visitDateTo', visitDateTo?.toISOString());

        departmentIds?.forEach(
            x => (params = params.append('departmentIds', x))
        );

        params = params.append(
            'includeChildDepartments',
            includeChildDepartments
        );

        methods?.forEach(x => (params = params.append('methods', x)));
        goalIds?.forEach(x => (params = params.append('goalIds', x)));
        managementTypes?.forEach(
            x => (params = params.append('managementTypes', x))
        );
        requestReasonIds?.forEach(
            x => (params = params.append('requestReasonIds', x))
        );
        operationIds?.forEach(x => (params = params.append('operationIds', x)));
        approvalStatuses?.forEach(
            x => (params = params.append('approvalStatuses', x))
        );
        years?.forEach(x => (params = params.append('years', `${x}`)));

        params = params.append('pageNumber', `${pageNumber}`);
        params = params.append('pageSize', `${pageSize}`);

        return this.httpClient
            .get<Result<TableResult<Benchmark>>>(
                environment.apiUrl + '/benchmark',
                {
                    params,
                }
            )
            .pipe(map(result => result.extra));
    }

    public get(id: string, forEdit: boolean = false): Observable<Benchmark> {
        return this.httpClient
            .get<Result<Benchmark>>(environment.apiUrl + '/benchmark/' + id, {
                params: new HttpParams().append('forEdit', `${forEdit}`),
            })
            .pipe(map(result => result.extra));
    }

    public create(benchmark: Benchmark): Observable<Benchmark> {
        return this.httpClient
            .post<Result<Benchmark>>(
                environment.apiUrl + '/benchmark',
                miscFunctions.objectToURLParams({
                    benchmark: JSON.stringify(benchmark),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(benchmark: Benchmark): Observable<Benchmark> {
        return this.httpClient
            .put<Result<Benchmark>>(
                environment.apiUrl + '/benchmark',
                miscFunctions.objectToURLParams({
                    benchmark: JSON.stringify(benchmark),
                })
            )
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(environment.apiUrl + '/benchmark/' + id)
            .pipe(map(result => result.messages[0]));
    }

    public firstApprove(id: string): Observable<string> {
        return this.httpClient
            .put<Result<any>>(
                `${environment.apiUrl}/benchmark/approve/first/${id}`,
                null
            )
            .pipe(map(res => res.messages[0]));
    }

    public secondApprove(id: string): Observable<string> {
        return this.httpClient
            .put<Result<any>>(
                `${environment.apiUrl}/benchmark/approve/second/${id}`,
                null
            )
            .pipe(map(res => res.messages[0]));
    }

    public submitReport(id: string, benchmark: Benchmark): Observable<string> {
        return this.httpClient
            .put<Result<any>>(
                `${environment.apiUrl}/benchmark/report/${id}`,
                miscFunctions.objectToURLParams({
                    benchmark: JSON.stringify(benchmark),
                })
            )
            .pipe(map(res => res.messages[0]));
    }

    public generalStatistics(): Observable<StatisticsGroup[]> {
        return this.httpClient
            .get<Result<StatisticsGroup[]>>(
                `${environment.apiUrl}/benchmark/statistics`
            )
            .pipe(map(res => res.extra));
    }
}
