import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListComponent } from './list/list.component';
import { NewComponent } from './new/new.component';
import { BenchmarkComponent } from './benchmark.component';
import { DetailComponent } from './detail/detail.component';
import { permissionList } from '@masar/common/constants';
import { DashboardComponent } from '@masar/pages/benchmark/dashboard/dashboard.component';

const routes: Routes = [
    {
        path: '',
        component: BenchmarkComponent,
        children: [
            {
                path: 'dashboard',
                component: DashboardComponent,
                data: {
                    title: 'translate_benchmarks_statistics',
                    breadcrumb: [
                        'translate_benchmarks',
                        'translate_benchmarks_statistics',
                    ],
                },
            },

            {
                path: '',
                component: ListComponent,
                data: {
                    title: 'translate_benchmarks',
                    breadcrumb: ['translate_benchmarks'],
                },
            },

            {
                path: 'new',
                component: NewComponent,
                data: {
                    title: 'translate_new_benchmark',
                    breadcrumb: [
                        'translate_benchmarks',
                        'translate_new_benchmark',
                    ],
                    permissionId: permissionList.benchmarkWrite,
                },
            },

            {
                path: 'edit/:id',
                component: NewComponent,
                data: {
                    title: 'translate_edit_benchmark',
                    breadcrumb: ['translate_benchmarks', 'translate_edit'],
                    permissionId: permissionList.benchmarkWrite,
                },
            },

            {
                path: 'detail/:id',
                component: DetailComponent,
                data: {
                    title: 'translate_benchmark_details',
                    breadcrumb: ['translate_benchmarks', 'translate_details'],
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class BenchmarkRoutingModule {}
