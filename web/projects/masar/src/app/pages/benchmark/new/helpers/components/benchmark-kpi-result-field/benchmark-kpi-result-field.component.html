<app-list-loading [items]="items">
    <div class="table-responsive mb-5">
        <table>
            <thead>
                <tr>
                    <th>{{ 'translate_year' | translate }}</th>
                    <th>{{ 'translate_kpi_name' | translate }}</th>
                    <th>{{ 'translate_target' | translate }}</th>
                    <th>{{ 'translate_result' | translate }}</th>
                    <th>{{ 'translate_achieved' | translate }}</th>
                    <th>{{ 'translate_partner_result' | translate }}</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of items; let idx = index">
                    <td>{{ item.result.year }}</td>
                    <td>{{ item.result.kpi.name }}</td>
                    <td>{{ item.result.target | round : 2 }}</td>
                    <td>{{ item.result.result | round : 2 }}</td>
                    <td>{{ item.result.achieved * 100 | round : 2 }}%</td>
                    <td>
                        <input
                            type="number"
                            [(ngModel)]="items[idx].partnerResult"
                        />
                    </td>

                    <!-- Controls-->
                    <td class="text-center">
                        <button
                            (click)="removeItem(item)"
                            class="btn btn-sm btn-danger"
                        >
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</app-list-loading>

<button
    (click)="showOwningKpiResultsDialog()"
    class="btn btn-info mx-auto flex flex-row items-center gap-2"
>
    <i class="fa-light fa-plus"></i>
    <span>{{ 'translate_add_new_result' | translate }}</span>
</button>
