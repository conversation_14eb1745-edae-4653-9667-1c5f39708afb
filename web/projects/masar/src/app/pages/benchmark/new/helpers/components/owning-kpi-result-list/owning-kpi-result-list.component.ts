import {
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { TableController } from '@masar/common/misc/table';
import { KpiResult } from '@masar/common/models';
import { MiscApiService } from '@masar/core/services';
import { SharedKpiResultService } from '@masar/shared/services';

@Component({
    selector: 'app-owning-kpi-result-list',
    templateUrl: './owning-kpi-result-list.component.html',
})
export class OwningKpiResultListComponent implements OnInit, OnDestroy {
    @Input() public excludedIds: string[];
    @Output() public selected = new EventEmitter<KpiResult>();

    public tableController: TableController<
        KpiResult,
        { keyword?: string; year?: number }
    >;

    public years: number[];

    public constructor(
        private sharedKpiResultService: SharedKpiResultService,
        miscApiService: MiscApiService
    ) {
        miscApiService.years().subscribe(items => (this.years = items));
    }

    public ngOnInit(): void {
        this.tableController = new TableController<
            KpiResult,
            { keyword?: string; year?: number }
        >(
            filter =>
                this.sharedKpiResultService.listOwningResults(
                    filter.data.keyword,
                    filter.data.year,
                    this.excludedIds,
                    filter.pageNumber,
                    filter.pageSize
                ),
            { data: { keyword: '', year: null } }
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }
}
