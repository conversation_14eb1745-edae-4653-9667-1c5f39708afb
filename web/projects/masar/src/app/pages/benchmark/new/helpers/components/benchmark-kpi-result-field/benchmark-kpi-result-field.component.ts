import { Component, forwardRef, NgModuleRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ModalService } from 'mnm-webapp';
import { BenchmarkKpiResult } from '@masar/common/models';
import { first } from 'rxjs/operators';
import { OwningKpiResultListComponent } from '../owning-kpi-result-list/owning-kpi-result-list.component';

@Component({
    selector: 'app-benchmark-kpi-result-field',
    templateUrl: './benchmark-kpi-result-field.component.html',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => BenchmarkKpiResultFieldComponent),
        },
    ],
})
export class BenchmarkKpiResultFieldComponent implements ControlValueAccessor {
    public items: BenchmarkKpiResult[] = [];

    private onChange: (data: BenchmarkKpiResult[]) => void;

    public constructor(
        private modalService: ModalService,
        private ngModuleRef: NgModuleRef<any>
    ) {}

    public writeValue(data: BenchmarkKpiResult[]): void {
        this.items = data || [];
    }

    public registerOnChange(fn: (data: BenchmarkKpiResult[]) => void): void {
        this.onChange = fn;
    }

    public registerOnTouched(_: any): void {
        // ignored
    }

    public setDisabledState?(_: boolean): void {
        // ignored
    }

    public async showOwningKpiResultsDialog(): Promise<void> {
        const component = await this.modalService.show(
            OwningKpiResultListComponent,
            {
                moduleRef: this.ngModuleRef,
                beforeInit: c => {
                    // Do not show already selected results.
                    c.excludedIds = this.items.map(x => x.result.id);
                },
            }
        );

        component.selected.pipe(first()).subscribe(result => {
            this.items.push({
                result,
                partnerResult: 0,
            });

            this.emitChanges();

            this.modalService.dismiss(component);
        });
    }

    public removeItem(item: BenchmarkKpiResult): void {
        this.items = this.items.filter(x => x.result.id !== item.result.id);
        this.emitChanges();
    }

    private emitChanges(): void {
        if (this.onChange) {
            this.onChange(
                this.items.filter(
                    x => x.partnerResult !== null && x.partnerResult !== null
                )
            );
        }
    }
}
