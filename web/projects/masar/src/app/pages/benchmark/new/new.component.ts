import {
    AfterViewInit,
    Component,
    ElementRef,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { MnmFormState } from '@masar/shared/components';
import { ActivatedRoute } from '@angular/router';
import { NotificationService } from 'mnm-webapp';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, Validators } from '@angular/forms';
import { finalize, first, takeUntil } from 'rxjs/operators';
import { BenchmarkService } from '../benchmark.service';
import {
    Benchmark,
    BenchmarkOtherManagement,
    BenchmarkVisitor,
    Item,
} from '@masar/common/models';
import { fields } from './fields';
import { HelperService, MiscApiService } from '@masar/core/services';
import { Observable, Subject } from 'rxjs';

@Component({
    selector: 'app-new',
    templateUrl: './new.component.html',
})
export class NewComponent implements OnInit, AfterViewInit, OnD<PERSON>roy {
    @ViewChild('departmentFieldRef')
    private departmentFieldRef: ElementRef;

    @ViewChild('operationListFieldRef')
    private operationListFieldRef: ElementRef;

    @ViewChild('otherManagementsFieldRef')
    private otherManagementsFieldRef: ElementRef;

    @ViewChild('visitorsFieldRef')
    private visitorsFieldRef: ElementRef;

    @ViewChild('kpiResultsFieldRef')
    private kpiResultsFieldRef: ElementRef;

    public isSubmitting = false;
    public mode: 'new' | 'edit';
    public formState: MnmFormState;

    public benchmarkId: string;

    public otherManagementTypes: Item[];

    public departmentParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;
    public departmentChildrenFetcher: (parentId: string) => Observable<Item[]>;

    public operationParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;
    public operationChildrenFetcher: (parentId: string) => Observable<Item[]>;

    private unsubscribeAll = new Subject();

    public constructor(
        private readonly helperService: HelperService,
        private notificationService: NotificationService,
        private benchmarkService: BenchmarkService,
        private translateService: TranslateService,
        private activatedRoute: ActivatedRoute,
        private miscApiService: MiscApiService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);

        this.loadItems();

        this.createFetchers();

        this.monitorForm();
    }

    public ngOnInit(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[0].path) {
                case 'new':
                    this.mode = 'new';
                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            this.benchmarkId = params.id;
                            this.benchmarkService
                                .get(this.benchmarkId, true)
                                .subscribe(item => {
                                    this.fillForm(item);
                                });
                        });
                    break;
            }
        });
    }

    public ngAfterViewInit(): void {
        this.formState.get('department').customInputField =
            this.departmentFieldRef;

        this.formState.get('operations').customInputField =
            this.operationListFieldRef;

        this.formState.get('otherManagements').customInputField =
            this.otherManagementsFieldRef;

        this.formState.get('visitors').customInputField = this.visitorsFieldRef;

        this.formState.get('kpiResults').customInputField =
            this.kpiResultsFieldRef;
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const observable =
            this.mode === 'new'
                ? this.benchmarkService.create(
                      this.formState.group.getRawValue()
                  )
                : this.benchmarkService.update(
                      this.formState.group.getRawValue()
                  );

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(item => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'benchmark'],
                    item.id
                );
            });
    }

    public validOtherManagements(
        otherManagements: BenchmarkOtherManagement[]
    ): BenchmarkOtherManagement[] {
        return otherManagements.filter(x => x.type && x.detail);
    }

    public validVisitors(visitors: BenchmarkVisitor[]): BenchmarkVisitor[] {
        return visitors.filter(
            x =>
                x.employeeNumber &&
                x.rank &&
                x.fullName &&
                x.department &&
                x.description &&
                x.employmentTitle &&
                x.phone &&
                x.email
        );
    }

    private loadItems(): void {
        this.miscApiService
            .getList('benchmark-other-management-type')
            .subscribe(items => (this.otherManagementTypes = items));

        this.miscApiService.setMiscItems(this.formState, [
            ['benchmark-management-type', 'managementType'],
            ['benchmark-entity-type', 'entityType'],
            ['benchmark-language', 'language'],
            ['benchmark-type', 'type'],
            ['benchmark-method', 'method'],
            ['benchmark-selection-reason', 'selectionReasons', undefined, true],
            ['benchmark-request-reason', 'requestReasons', undefined, true],
            ['partner', 'partner', undefined, true],
            ['strategic-goal', 'goals', undefined, true],
        ]);
    }

    private createFetchers(): void {
        this.departmentParentFetcher = (childId: string) =>
            this.miscApiService.parentDepartment(childId);
        this.departmentChildrenFetcher = (parentId: string) =>
            this.miscApiService.departments({
                parentDepartmentId: parentId,
                respectHierarchy: true,
            });

        this.operationParentFetcher = (childId: string) =>
            this.miscApiService.parentOperation(childId);
        this.operationChildrenFetcher = (parentId: string) =>
            this.miscApiService.operations({
                parentOperationId: parentId,
                respectHierarchy: true,
            });
    }

    private fillForm(item: Benchmark): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }

    private monitorForm(): void {
        // Management type monitoring
        this.formState.group.controls['managementType'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const operations = this.formState.group.controls['operations'];
                const otherManagements =
                    this.formState.group.controls['otherManagements'];

                operations.disable();
                operations.setValue([]);

                otherManagements.disable();
                otherManagements.setValue([]);

                switch (value) {
                    case 'operation':
                        operations.enable();
                        break;
                    case 'other':
                        otherManagements.enable();
                        break;
                }

                operations.updateValueAndValidity();
                otherManagements.updateValueAndValidity();
            });

        // Entity type monitoring
        this.formState.group.controls['entityType'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const entityName = this.formState.group.controls['entityName'];
                const partner = this.formState.group.controls['partner'];

                entityName.disable();
                entityName.setValidators([]);
                entityName.setValue('');

                partner.disable();
                partner.setValidators([]);
                partner.setValue(null);

                switch (value) {
                    case 'partner':
                        partner.setValidators([Validators.required]);
                        partner.enable();
                        break;
                    case 'country':
                    case 'other':
                        entityName.setValidators([Validators.required]);
                        entityName.enable();
                        break;
                }

                partner.updateValueAndValidity();
                entityName.updateValueAndValidity();
            });

        [
            'requestReasons',
            'otherRequestReasons',
            // 'selectionReasons',
            // 'otherSelectionReasons',
        ].forEach(x => {
            const control = this.formState.group.controls[x];
            control.setValidators([Validators.required]);
            control.updateValueAndValidity();
        });

        // Request reason monitoring
        this.formState.group.controls['requestReasons'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const correspondingControl =
                    this.formState.group.controls['otherRequestReasons'];
                if (!value || value.length === 0) {
                    correspondingControl.setValidators([Validators.required]);
                } else {
                    correspondingControl.setValidators([]);
                }
                correspondingControl.updateValueAndValidity({
                    emitEvent: false,
                });
            });

        this.formState.group.controls['otherRequestReasons'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const correspondingControl =
                    this.formState.group.controls['requestReasons'];
                if (!value) {
                    correspondingControl.setValidators([Validators.required]);
                } else {
                    correspondingControl.setValidators([]);
                }
                correspondingControl.updateValueAndValidity({
                    emitEvent: false,
                });
            });

        // Selection reason monitoring
        // this.formState.group.controls['selectionReasons'].valueChanges
        //     .pipe(takeUntil(this.unsubscribeAll))
        //     .subscribe(value => {
        //         const correspondingControl =
        //             this.formState.group.controls['otherSelectionReasons'];
        //         if (!value || value.length === 0) {
        //             correspondingControl.setValidators([Validators.required]);
        //         } else {
        //             correspondingControl.setValidators([]);
        //         }
        //         correspondingControl.updateValueAndValidity({
        //             emitEvent: false,
        //         });
        //     });

        // this.formState.group.controls['otherSelectionReasons'].valueChanges
        //     .pipe(takeUntil(this.unsubscribeAll))
        //     .subscribe(value => {
        //         const correspondingControl =
        //             this.formState.group.controls['selectionReasons'];
        //         if (!value) {
        //             correspondingControl.setValidators([Validators.required]);
        //         } else {
        //             correspondingControl.setValidators([]);
        //         }
        //         correspondingControl.updateValueAndValidity({
        //             emitEvent: false,
        //         });
        //     });
    }
}
