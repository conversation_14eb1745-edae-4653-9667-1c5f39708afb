import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { permissionList } from '@masar/common/constants';
import { Benchmark } from '@masar/common/models';
import { BenchmarkService } from '../benchmark.service';

@Component({
    selector: 'app-detail',
    templateUrl: './detail.component.html',
})
export class DetailComponent {
    public benchmarkId: string;
    public benchmark: Benchmark;

    public permissionList = permissionList;

    public constructor(
        benchmarkService: BenchmarkService,
        activatedRoute: ActivatedRoute
    ) {
        this.benchmarkId = activatedRoute.snapshot.paramMap.get('id');
        benchmarkService
            .get(this.benchmarkId)
            .subscribe(item => (this.benchmark = item));
    }
}
