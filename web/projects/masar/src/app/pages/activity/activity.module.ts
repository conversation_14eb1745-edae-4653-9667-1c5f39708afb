import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivityComponent } from './activity.component';
import { ListComponent } from './list/list.component';
import { ActivityRoutingModule } from './activity.routing';
import { NewComponent } from './new/new.component';
import { MasarModule } from '@masar/features/masar/masar.module';
import { SharedModule } from '@masar/shared/shared.module';
import { ActivityService } from './activity.service';
import { FormsModule } from '@angular/forms';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { DetailComponent } from './detail/detail.component';
import { TranslationModule } from '@ng-omar/translation';

@NgModule({
    declarations: [
        ActivityComponent,
        ListComponent,
        NewComponent,
        DetailComponent,
    ],
    imports: [
        CommonModule,
        ActivityRoutingModule,
        MasarModule,
        TranslationModule,
        SharedModule,
        FormsModule,
        SweetAlert2Module,
    ],
    providers: [ActivityService],
})
export class ActivityModule {}
