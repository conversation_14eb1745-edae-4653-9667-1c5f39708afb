import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { environment } from '@masar/env/environment';
import { Observable } from 'rxjs/internal/Observable';
import { first } from 'rxjs/operators';
import { TableResult } from '@masar/common/misc/table';
import { Activity, Innovation } from '@masar/common/models';
import { ActivityService } from '../activity.service';

@Component({
    selector: 'app-detail',
    templateUrl: './detail.component.html',
})
export class DetailComponent {
    public readonly environment = environment;

    public activity: Activity;
    public activityId: string;

    public constructor(
        private activityService: ActivityService,
        private activatedRoute: ActivatedRoute
    ) {
        this.activatedRoute.params.pipe(first()).subscribe(params => {
            {
                this.activityId = params.id;
                this.activityService
                    .get(this.activityId)
                    .subscribe(item => (this.activity = item));
            }
        });
    }

    // Innovation linking methods
    public listLinkedInnovations(): (
        title: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Innovation>> {
        return (title: string, pageNumber: number, pageSize: number) =>
            this.activityService.listLinkedInnovations(
                this.activityId,
                title,
                pageNumber,
                pageSize
            );
    }

    public linkInnovation(): (innovationId: string) => Observable<string> {
        return (innovationId: string) =>
            this.activityService.linkInnovation(this.activityId, innovationId);
    }

    public unlinkInnovation(): (innovationId: string) => Observable<string> {
        return (innovationId: string) =>
            this.activityService.unlinkInnovation(
                this.activityId,
                innovationId
            );
    }
}
