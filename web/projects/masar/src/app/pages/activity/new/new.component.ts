import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { finalize, first } from 'rxjs/operators';
import { Activity } from '@masar/common/models';
import { MnmFormState } from '@masar/shared/components';
import { ActivityService } from '../activity.service';
import { fields } from './fields';
import { HelperService } from '@masar/core/services';

@Component({
    selector: 'app-new',
    templateUrl: './new.component.html',
})
export class NewComponent implements OnInit {
    public formState: MnmFormState;
    public mode: 'new' | 'edit';
    public isSubmitting = false;

    public constructor(
        private activatedRoute: ActivatedRoute,
        private activityService: ActivityService,
        private notificationService: NotificationService,
        private readonly helperService: HelperService,
        private translateService: TranslateService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);
    }

    public ngOnInit(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[0].path) {
                case 'new':
                    this.mode = 'new';
                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            this.activityService
                                .get(params.id, true)
                                .subscribe(result => {
                                    this.fillForm(result);
                                });
                        });
            }
        });
    }

    public submit(): void {
        this.formState.setTriedToSubmit();
        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const observable =
            this.mode === 'new'
                ? this.activityService.create(
                      this.formState.group.getRawValue()
                  )
                : this.activityService.update(
                      this.formState.group.getRawValue()
                  );
        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(activity => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'activity'],
                    activity.id
                );
            });
    }

    public fillForm(item: Activity): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }
}
