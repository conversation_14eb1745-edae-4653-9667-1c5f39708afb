import { Component } from '@angular/core';
import { flippingCard } from '@masar/common/animations';
import { Tournament } from '@masar/common/models';
import { ExcellenceService } from '../excellence.service';

@Component({
    selector: 'app-tournament-list',
    templateUrl: './tournament-list.component.html',
    animations: [...flippingCard],
})
export class TournamentListComponent {
    public tournaments: Tournament[];

    public constructor(excellenceService: ExcellenceService) {
        excellenceService
            .listTournaments()
            .subscribe(items => (this.tournaments = items));
    }
}
