<a
    [routerLink]="['', 'excellence', type, item.id]"
    [ngClass]="{
        'bg-primary': type !== 'standard',
        'hover:bg-primary-700': type !== 'standard',
        'bg-primary-50': type === 'standard',
        'hover:bg-primary-100': type === 'standard',
        'border': type === 'standard',
        'border-primary-200': type === 'standard',
        'text-white': type !== 'standard',
        'hover:text-white': type !== 'standard',
        'text-primary': type === 'standard',
        'hover:text-primary': type === 'standard'
    }"
    class="flex h-full w-full cursor-pointer flex-row items-center gap-2 rounded p-4 no-underline transition-colors hover:no-underline"
>
    <!-- Logo -->
    <div
        *ngIf="type === 'tournament'"
        class="mx-auto h-16 w-16 flex-shrink-0 overflow-hidden rounded-full border-2 border-white bg-gray-200"
        style="flex-basis: 4rem"
    >
        <app-image
            [imageObservable]="imageApiService.tournament(item.id)"
            style="height: 100%"
        ></app-image>
    </div>

    <!-- Details -->
    <div class="flex flex-grow flex-col items-center justify-center">
        <!-- Name -->
        <div class="mb-5 text-center text-xl font-bold">{{ item.name }}</div>

        <!-- Counts -->
        <div class="flex flex-row gap-3">
            <!-- Weight count -->
            <div
                *ngIf="type === 'pillar'"
                class="flex flex-col items-center gap-1"
            >
                <!-- Label -->
                <span>{{ 'translate_weight' | translate }}</span>

                <!-- Count -->
                <div
                    class="badge bg-white text-center text-base font-bold text-primary"
                >
                    {{ $any(item).weight * 100 }}%
                </div>
            </div>

            <!-- Kpis count -->
            <div class="flex flex-col items-center gap-1">
                <!-- Label -->
                <span>{{ 'translate_results' | translate }}</span>

                <!-- Count -->
                <div
                    [ngClass]="{
                        'border': type === 'standard',
                        'border-primary-200': type === 'standard'
                    }"
                    class="badge bg-white text-center text-base font-bold text-primary"
                >
                    {{ item.kpiCount }}
                </div>
            </div>

            <!-- Capability count -->
            <div class="flex flex-col items-center gap-1">
                <!-- Label -->
                <span>{{ 'translate_capabilities' | translate }}</span>

                <!-- Count -->
                <div
                    [ngClass]="{
                        'border': type === 'standard',
                        'border-primary-200': type === 'standard'
                    }"
                    class="badge bg-white text-center text-base font-bold text-primary"
                >
                    {{ item.capabilityCount }}
                </div>
            </div>
        </div>
    </div>
</a>
