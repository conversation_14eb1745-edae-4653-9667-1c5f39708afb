<app-page pageTitle="{{ tournament?.name }}">
    <ng-container content>
        <!-- Pillars label -->
        <h3 class="mb-2 text-xl font-bold">
            {{ 'translate_pillars' | translate }}
        </h3>

        <!-- Pillars grid -->
        <app-list-loading content [items]="tournament?.standards">
            <div
                [@flippingCardGrid]="tournament?.pillars.length"
                class="mb-5 grid grid-cols-1 gap-5 md:grid-cols-3"
            >
                <app-excellence-item
                    [@flippingCard]
                    *ngFor="let item of tournament?.pillars"
                    [item]="item"
                    type="pillar"
                ></app-excellence-item>
            </div>
        </app-list-loading>

        <!-- Standards label -->
        <h3 class="mb-2 text-xl font-bold">
            {{ 'translate_excellence_standards' | translate }}
        </h3>

        <!-- Standards grid -->
        <app-list-loading content [items]="tournament?.standards">
            <div
                [@flippingCardGrid]="tournament?.standards.length"
                class="mb-5 grid grid-cols-1 gap-5 md:grid-cols-3"
            >
                <app-excellence-item
                    [@flippingCard]
                    *ngFor="let item of tournament?.standards"
                    [item]="item"
                    type="standard"
                ></app-excellence-item>
            </div>
        </app-list-loading>
    </ng-container>
</app-page>
