<app-page pageTitle="{{ pillar?.tournament.name }}">
    <ng-container content>
        <!-- Pillar name -->
        <h2 class="flex flex-row content-start items-center gap-2">
            <!-- Tournament logo -->
            <div
                class="h-16 w-16 flex-shrink overflow-hidden rounded-full border-2 border-primary bg-gray-200"
            >
                <app-image
                    *ngIf="pillar"
                    [imageObservable]="
                        imageApiService.tournament(pillar.tournament.id)
                    "
                    style="height: 100%"
                ></app-image>
            </div>

            <!-- Pillar name -->
            <h2 class="flex-grow gap-2 text-2xl font-bold text-primary">
                <ng-container *appWaitUntilLoaded="pillar">
                    {{ pillar.name }} ({{ pillar.weight * 100 }}%)
                </ng-container>
            </h2>

            <!-- Achieved -->
            <app-progress-ring
                [value]="pillar?.achieved || 0"
                [radius]="30"
                [strokeWidth]="6"
            ></app-progress-ring>
        </h2>

        <hr class="my-5" />

        <!-- Standards label -->
        <h3 class="mb-2 text-xl font-bold">
            {{ 'translate_excellence_standards' | translate }}
        </h3>

        <!-- Standards grid -->
        <app-list-loading content [items]="pillar?.standards">
            <div
                [@flippingCardGrid]="pillar?.standards.length"
                class="mb-5 grid grid-cols-1 gap-5 md:grid-cols-3"
            >
                <app-excellence-item
                    [@flippingCard]
                    *ngFor="let item of pillar?.standards"
                    [item]="item"
                    type="standard"
                ></app-excellence-item>
            </div>
        </app-list-loading>
    </ng-container>
</app-page>
