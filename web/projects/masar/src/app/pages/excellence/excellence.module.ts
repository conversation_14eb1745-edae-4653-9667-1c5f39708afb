import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ExcellenceRoutingModule } from './excellence.routing';
import { SharedModule } from '@masar/shared/shared.module';
import { NgCircleProgressModule } from 'ng-circle-progress';
import { ExcellenceService } from './excellence.service';
import { MasarModule } from '@masar/features/masar/masar.module';
import { ExcellenceComponent } from './excellence.component';
import { ExcellenceItemComponent } from './components/excellence-item/excellence-item.component';
import { FormsModule } from '@angular/forms';
import { TournamentListComponent } from './tournament-list/tournament-list.component';
import { TournamentDetailComponent } from './tournament-detail/tournament-detail.component';
import { PillarDetailComponent } from './pillar-detail/pillar-detail.component';
import { StandardDetailComponent } from './standard-detail/standard-detail.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { KpiSharedModule } from '@masar/features/kpi-shared/kpi-shared.module';
import { CapabilitySharedModule } from '@masar/features/capability-shared/capability-shared.module';
import { TranslationModule } from '@ng-omar/translation';

@NgModule({
    declarations: [
        ExcellenceComponent,
        TournamentListComponent,
        TournamentDetailComponent,
        PillarDetailComponent,
        StandardDetailComponent,

        ExcellenceItemComponent,
    ],
    imports: [
        CommonModule,
        ExcellenceRoutingModule,
        TranslationModule,
        SharedModule,
        MasarModule,
        FormsModule,
        NgCircleProgressModule,
        NgSelectModule,
        KpiSharedModule,
        CapabilitySharedModule,
        NgCircleProgressModule.forRoot({}),
    ],
    providers: [ExcellenceService],
})
export class ExcellenceModule {}
