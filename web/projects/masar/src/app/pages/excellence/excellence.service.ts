import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Result } from 'mnm-webapp';
import { environment } from '@masar/env/environment';
import { map } from 'rxjs/operators';
import {
    Capability,
    Kpi,
    LibraryFile,
    Pillar,
    Standard,
    Tournament,
} from '@masar/common/models';
import { TableController } from '@masar/common/misc/table';

@Injectable()
export class ExcellenceService {
    public constructor(private httpClient: HttpClient) {}

    public listTournaments(): Observable<Tournament[]> {
        return this.httpClient
            .get<Result<Tournament[]>>(
                `${environment.apiUrl}/excellence/tournament`
            )
            .pipe(map(res => res.extra));
    }

    public getTournament(tournamentId: string): Observable<Tournament> {
        return this.httpClient
            .get<Result<Tournament>>(
                `${environment.apiUrl}/excellence/tournament/${tournamentId}`
            )
            .pipe(map(res => res.extra));
    }

    // public listPillars({ tournamentId = null } = {}): Observable<Pillar[]> {
    //     return this.httpClient
    //         .get<Result<Pillar[]>>(
    //             `${environment.apiUrl}/excellence/pillar`,
    //             {
    //                 params: new HttpParams().append(
    //                     'tournamentId',
    //                     tournamentId
    //                 ),
    //             }
    //         )
    //         .pipe(map(res => res.extra));
    // }

    public getPillar(pillarId: string): Observable<Pillar> {
        return this.httpClient
            .get<Result<Pillar>>(
                `${environment.apiUrl}/excellence/pillar/${pillarId}`
            )
            .pipe(map(res => res.extra));
    }

    // public listStandards({
    //     tournamentId = null,
    //     pillarId = null,
    // } = {}): Observable<Standard[]> {
    //     return this.httpClient
    //         .get<Result<Standard[]>>(
    //             `${environment.apiUrl}/excellence/standard`,
    //             {
    //                 params: new HttpParams()
    //                     .append('tournamentId', tournamentId)
    //                     .append('pillarId', pillarId),
    //             }
    //         )
    //         .pipe(map(res => res.extra));
    // }

    public getStandard(standardId: string): Observable<Standard> {
        return this.httpClient
            .get<Result<Standard>>(
                `${environment.apiUrl}/excellence/standard/${standardId}`
            )
            .pipe(map(res => res.extra));
    }

    // public listPrinciples({
    //     tournamentId = null,
    //     pillarId = null,
    //     standardId = null,
    // } = {}): Observable<Principle[]> {
    //     return this.httpClient
    //         .get<Result<Principle[]>>(
    //             `${environment.apiUrl}/excellence/principle`,
    //             {
    //                 params: new HttpParams()
    //                     .append('tournamentId', tournamentId)
    //                     .append('pillarId', pillarId)
    //                     .append('standardId', standardId),
    //             }
    //         )
    //         .pipe(map(res => res.extra));
    // }

    // public getPrinciple(principleId: string): Observable<Principle> {
    //     return this.httpClient
    //         .get<Result<Principle>>(
    //             `${environment.apiUrl}/excellence/principle/${principleId}`
    //         )
    //         .pipe(map(res => res.extra));
    // }

    public capabilities(
        { tournamentId = null, pillarId = null, standardId = null } = {},
        keyword: string = '',
        pageNumber: number = 0,
        pageSize: number = 20
    ): Observable<TableController<Capability>> {
        return this.httpClient
            .get<Result<TableController<Capability>>>(
                `${environment.apiUrl}/excellence/capability`,
                {
                    params: new HttpParams()
                        .append('tournamentId', tournamentId)
                        .append('pillarId', pillarId)
                        .append('standardId', standardId)
                        .append('keyword', keyword)
                        .append('pageNumber', pageNumber)
                        .append('pageSize', pageSize),
                }
            )
            .pipe(map(res => res.extra));
    }

    public libraryFiles(
        {
            tournamentId = null,
            pillarId = null,
            standardId = null,
            principleId = null,
        } = {},
        keyword: string = '',
        pageNumber: number = 0,
        pageSize: number = 20
    ): Observable<TableController<LibraryFile>> {
        return this.httpClient
            .get<Result<TableController<LibraryFile>>>(
                `${environment.apiUrl}/excellence/library-file`,
                {
                    params: new HttpParams()
                        .append('tournamentId', tournamentId)
                        .append('pillarId', pillarId)
                        .append('standardId', standardId)
                        .append('principleId', principleId)
                        .append('keyword', keyword)
                        .append('pageNumber', pageNumber)
                        .append('pageSize', pageSize),
                }
            )
            .pipe(map(res => res.extra));
    }

    public kpis(
        { tournamentId = null, pillarId = null, standardId = null } = {},
        keyword: string = '',
        pageNumber: number = 0,
        pageSize: number = 20
    ): Observable<TableController<Kpi>> {
        return this.httpClient
            .get<Result<TableController<Kpi>>>(
                `${environment.apiUrl}/excellence/kpi`,
                {
                    params: new HttpParams()
                        .append('tournamentId', tournamentId)
                        .append('pillarId', pillarId)
                        .append('standardId', standardId)
                        .append('keyword', keyword)
                        .append('pageNumber', pageNumber)
                        .append('pageSize', pageSize),
                }
            )
            .pipe(map(res => res.extra));
    }
}
