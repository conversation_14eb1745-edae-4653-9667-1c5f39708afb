import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { SharedModule } from '@masar/shared/shared.module';
import { TranslationModule } from '@ng-omar/translation';
import { EasterModule } from '@masar/features/easter/easter.module';
import { ForgetPasswordRouting } from './forget-password.routing';
import { ForgetPasswordComponent } from './forget-password.component';
import { ForgetPasswordService } from './forget-password.service';
@NgModule({
    declarations: [ForgetPasswordComponent],
    imports: [
        CommonModule,
        ForgetPasswordRouting,
        FormsModule,
        TranslationModule,
        SharedModule,
        EasterModule,
    ],
    providers: [ForgetPasswordService],
})
export class ForgetPasswordModule {}
