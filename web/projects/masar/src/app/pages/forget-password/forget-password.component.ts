import { Component, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { ForgetPasswordService } from './forget-password.service';
import { finalize } from 'rxjs/operators';
import { isEmailValid } from '@masar/common/utils';

@Component({
    selector: 'app-forget-password',
    templateUrl: './forget-password.component.html',
})
export class ForgetPasswordComponent implements OnDestroy {
    public email = '';
    public isSubmitting = false;
    private unsubscribeAll = new Subject();

    public constructor(
        private translateService: TranslateService,
        private notificationService: NotificationService,
        private forgetPasswordService: ForgetPasswordService
    ) {}

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public submit(): void {
        if (this.isSubmitting) return;
        const messages = [
            'translate_email_should_be_filled',
            'translate_email_should_be_valid',
        ];
        if (!this.email) {
            this.notificationService.notifyError(
                this.translateService.instant(messages[0])
            );
            return;
        }

        if (!isEmailValid(this.email)) {
            this.notificationService.notifyError(
                this.translateService.instant(messages[1])
            );
            return;
        }
        this.isSubmitting = true;
        this.forgetPasswordService
            .forgetPassword(this.email)
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(res => {
                this.notificationService.notifyInfo(res);
                this.email = '';
            });
    }
}
