import { Loader } from '@masar/common/misc/loader';
import { Department, Item, Team, User } from '@masar/common/models';
import { MiscApiService } from '@masar/core/services';
import { MnmFormState } from '@masar/shared/components';
import { takeUntil } from 'rxjs/operators';
import { Validators } from '@angular/forms';
import { Subject } from 'rxjs';

export const planSharedFunctions = {
    loadAssigneeTypeItems: (
        miscApiService: MiscApiService,
        formState: MnmFormState,
        parentPlanEntity: {
            id: string;
            assignedDepartment: Department;
            assignedTeam: Team;
            assignedUser: User;
        } = null
    ) => {
        // Load assignee type.
        miscApiService.getList('plan-assignee-type').subscribe(items => {
            if (parentPlanEntity !== null) {
                // If the parent is assigned to a department, then
                // for this entity, the assigned type can be anything
                // except a team.
                if (parentPlanEntity.assignedDepartment) {
                    items = items.filter(x => x.id !== 'team');
                }
                // If it is assigned to a team, then everything allowed
                // except a department.
                else if (parentPlanEntity.assignedTeam) {
                    items = items.filter(x => x.id !== 'department');
                }

                // If it is assigned to a user, then it can only be assigned
                // to the same user.
                else if (parentPlanEntity.assignedUser) {
                    items = items.filter(
                        x => x.id !== 'department' && x.id !== 'team'
                    );
                }
            }

            formState.get('assigneeType').items = items;
        });
    },

    loadKpiItems: (miscApiService: MiscApiService, formState: MnmFormState) => {
        formState.get('kpis').loader = new Loader<Item>(keyword => {
            const assigneeType = formState.group.controls['assigneeType'].value;
            let assignedDepartment: Item = null;

            if (assigneeType === 'department') {
                assignedDepartment =
                    formState.group.controls['assignedDepartment'].value;
            }

            return miscApiService.kpis({
                keyword,
                departmentIds: assignedDepartment
                    ? [
                          /*assignedDepartment.id*/
                      ]
                    : [],
            });
        });
    },

    monitorAssigneeType: (
        formState: MnmFormState,
        unsubscribeAll: Subject<any>
    ) => {
        const assignedFields = [
            'assignedDepartment',
            'assignedTeam',
            'assignedUser',
        ];

        formState.group.controls['assigneeType'].valueChanges
            .pipe(takeUntil(unsubscribeAll))
            .subscribe(value => {
                // Clean all
                assignedFields.forEach(x => {
                    formState.get(x).hide = true;
                    const control = formState.group.controls[x];
                    control.setValue(null);
                    control.setValidators([]);
                    control.disable();
                });

                if (value === null) return;

                let controlName = '';

                if (value === 'department') controlName = 'assignedDepartment';
                else if (value === 'team') controlName = 'assignedTeam';
                else if (value === 'user') controlName = 'assignedUser';
                else return;

                const control = formState.group.controls[controlName];
                formState.get(controlName).hide = false;
                control.setValidators([Validators.required]);
                control.enable();

                formState.group.updateValueAndValidity();
            });
    },
};
