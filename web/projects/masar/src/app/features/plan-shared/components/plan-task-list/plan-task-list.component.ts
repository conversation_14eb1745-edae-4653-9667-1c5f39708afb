import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Item, PlanTask } from '@masar/common/models';
import { finalize } from 'rxjs/operators';
import { TableController } from '@masar/common/misc/table/table-controller';
import { NotificationService } from 'mnm-webapp';
import { MiscApiService } from '@masar/core/services';
import { PlanTaskListService } from './plan-task-list.service';
import { permissionList } from '@masar/common/constants';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { getOrderByAsString } from '@masar/common/utils';

interface Filter {
    keyword?: string;
    years?: string[];
    assigneeType?: string;
    departmentIds?: string[];
    includeChildDepartments?: boolean;
    teamIds?: string[];
    userIds?: string[];
    planIds?: string[];
    categoryIds?: string[];
    from?: Date;
    to?: Date;
    progressStatus?: '' | 'in_progress' | 'completed';
}

interface HiddenFilters {
    keyword?: boolean;
    assigneeType?: boolean;
    departmentIds?: boolean;
    teamIds?: boolean;
    userIds?: boolean;
    planIds?: boolean;
    category?: boolean;
    from?: boolean;
    to?: boolean;
}

interface HiddenColumns {
    progress?: boolean;
    name?: boolean;
    plan?: boolean;
    assigned?: boolean;
    from?: boolean;
    to?: boolean;
    isApproved?: boolean;
    weight?: boolean;
    subtasksCount?: boolean;
}

@Component({
    selector: 'app-plan-task-list',
    templateUrl: './plan-task-list.component.html',
    providers: [PlanTaskListService],
})
export class PlanTaskListComponent implements OnInit, OnDestroy {
    @Input() public defaultFilter: Filter = {};
    @Input() public hiddenFilter: HiddenFilters = {};
    @Input() public hiddenColumns: HiddenColumns = {};
    @Input() public considerPlanFilterAsBase: boolean = false;
    @Input() public isFilterInUrl = false;

    public tableController: TableController<PlanTask, Filter>;

    public years: Item[];
    public assigneeTypes: Item[];
    public departments: Item[];
    public teams: Item[];
    public users: Item[];
    public plans: Item[];
    public progressStatuses: Item[];
    public categories: Item[];

    public currentlyDeleting: string[] = [];

    public permissionList = permissionList;

    public constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private notificationService: NotificationService,
        private planTaskListService: PlanTaskListService,
        miscApiService: MiscApiService,
        translateService: TranslateService
    ) {
        miscApiService
            .getList('plan-assignee-type')
            .subscribe(items => (this.assigneeTypes = items));

        miscApiService
            .getList('creation-year')
            .subscribe(items => (this.years = items));

        miscApiService
            .departments()
            .subscribe(items => (this.departments = items));

        miscApiService
            .getList('team', { linkedWith: 'plan_task' }, true)
            .subscribe(items => (this.teams = items));

        miscApiService
            .getList('user', { linkedWith: 'plan_task' }, true)
            .subscribe(items => (this.users = items));

        miscApiService
            .getList('plan', undefined, true)
            .subscribe(items => (this.plans = items));
        miscApiService
            .getList('plan-task-category')
            .subscribe(items => (this.categories = items));

        this.progressStatuses = [
            {
                id: 'completed',
                name: translateService.instant('translate_completed'),
            },
            {
                id: 'in_progress',
                name: translateService.instant('translate_in_progress'),
            },
        ];
    }

    public ngOnInit(): void {
        this.tableController = new TableController<PlanTask, Filter>(
            filter =>
                this.planTaskListService.list(
                    filter.data.keyword,
                    filter.data.years,
                    filter.data.assigneeType,
                    filter.data.departmentIds,
                    filter.data.includeChildDepartments,
                    filter.data.teamIds,
                    filter.data.userIds,
                    filter.data.planIds,
                    filter.data.categoryIds,
                    filter.data.from ? new Date(filter.data.from) : null,
                    filter.data.to ? new Date(filter.data.to) : null,
                    filter.data.progressStatus ?? '',
                    getOrderByAsString(filter.orderBy),
                    filter.pageNumber,
                    filter.pageSize,
                    this.considerPlanFilterAsBase
                ),
            {
                data: {
                    keyword: this.defaultFilter.keyword || '',
                    years: this.defaultFilter.years || [],
                    departmentIds: this.defaultFilter.departmentIds || [],
                    teamIds: this.defaultFilter.teamIds || [],
                    userIds: this.defaultFilter.userIds || [],
                    planIds: this.defaultFilter.planIds || [],
                    categoryIds: this.defaultFilter.categoryIds || [],
                    from: this.defaultFilter.from || null,
                    to: this.defaultFilter.to || null,
                    progressStatus: this.defaultFilter.progressStatus || null,
                },
            },
            this.isFilterInUrl
                ? {
                      routingControls: {
                          router: this.router,
                          activatedRoute: this.activatedRoute,
                      },
                  }
                : {}
        );

        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public delete(item: PlanTask): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeleting.push(item.id);
        this.planTaskListService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }
}
