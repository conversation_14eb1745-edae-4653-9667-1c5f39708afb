import { Injectable } from '@angular/core';
import { Result } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient, HttpParams } from '@angular/common/http';
import { PlanTask } from '@masar/common/models';
import { environment } from '@masar/env/environment';
import { TableResult } from '@masar/common/misc/table';

@Injectable()
export class PlanTaskListService {
    public constructor(private httpClient: HttpClient) {}

    public list(
        keyword: string,
        years: string[],
        assigneeType: string | null,
        departmentIds: string[],
        includeChildDepartments: boolean,
        teamIds: string[],
        userIds: string[],
        planIds: string[],
        categoryIds: string[],
        from: Date,
        to: Date,
        progressStatus: '' | 'in_progress' | 'completed',
        orderBy: string,
        pageNumber: number,
        pageSize: number = 20,
        considerPlanFilterAsBase: boolean = false
    ): Observable<TableResult<PlanTask>> {
        let params = new HttpParams();
        params = params
            .append('keyword', keyword)
            .append('from', from?.toISOString())
            .append('to', to?.toISOString())
            .append('progressStatus', progressStatus)
            .append('considerPlanFilterAsBase', `${considerPlanFilterAsBase}`)
            .append('assigneeType', assigneeType || '');

        years.forEach(item => (params = params.append('years', item)));

        departmentIds.forEach(
            item => (params = params.append('departmentIds', item))
        );
        params = params.append(
            'includeChildDepartments',
            includeChildDepartments
        );
        teamIds.forEach(item => (params = params.append('teamIds', item)));
        userIds.forEach(item => (params = params.append('userIds', item)));
        planIds.forEach(item => (params = params.append('planIds', item)));
        categoryIds.forEach(
            item => (params = params.append('categoryIds', item))
        );

        params = params.append('orderBy', orderBy);
        params = params.append('pageNumber', `${pageNumber}`);
        params = params.append('pageSize', `${pageSize}`);

        return this.httpClient
            .get<Result<TableResult<PlanTask>>>(
                environment.apiUrl + '/plan-task',
                {
                    params,
                }
            )
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(environment.apiUrl + '/plan-task/' + id)
            .pipe(map(result => result.messages[0]));
    }
}
