<!-- Filter -->
<app-filter-result-box>
    <!-- Keyword -->
    <app-search-input
        *ngIf="!hiddenFilter.keyword"
        [placeholder]="'translate_search_by_keyword'"
        [(ngModel)]="tableController.filter.data.keyword"
        [tableController]="tableController"
    ></app-search-input>

    <!-- Plans -->
    <ng-select
        *ngIf="!hiddenFilter.planIds"
        [items]="plans"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_operational_plans' | translate }}"
        [(ngModel)]="tableController.filter.data.planIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Tasks -->
    <ng-select
        *ngIf="!hiddenFilter.taskIds"
        [items]="tasks"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_tasks' | translate }}"
        [(ngModel)]="tableController.filter.data.taskIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- From -->
    <div *ngIf="!hiddenFilter.from" class="flex flex-row items-center">
        <input
            class="flex-grow"
            #from="appFlatpickr"
            type="date"
            appFlatpickr
            [(ngModel)]="tableController.filter.data.from"
            (flatPickrChange)="tableController.filter$.next(true)"
            placeholder="{{ 'translate_from' | translate }}"
        />
        <button (click)="from.clear()" class="btn btn-sm btn-primary ms-1">
            <i class="fa-light fa-times"></i>
        </button>
    </div>

    <!-- To -->
    <div *ngIf="!hiddenFilter.to" class="flex flex-row items-center">
        <input
            class="flex-grow"
            #to="appFlatpickr"
            type="date"
            appFlatpickr
            [(ngModel)]="tableController.filter.data.to"
            (flatPickrChange)="tableController.filter$.next(true)"
            placeholder="{{ 'translate_to' | translate }}"
        />
        <button (click)="to.clear()" class="btn btn-sm btn-primary ms-1">
            <i class="fa-light fa-times"></i>
        </button>
    </div>

    <!-- Assignee Type -->
    <ng-select
        *ngIf="!hiddenFilter.assigneeType"
        [items]="assigneeTypes"
        bindValue="id"
        bindLabel="name"
        [placeholder]="'translate_assignee_type' | translate"
        [(ngModel)]="tableController.filter.data.assigneeType"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Teams -->
    <ng-select
        *ngIf="
            !hiddenFilter.teamIds &&
            tableController.filter.data.assigneeType === 'team'
        "
        [items]="teams"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_teams' | translate }}"
        [(ngModel)]="tableController.filter.data.teamIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Users -->
    <ng-select
        *ngIf="
            !hiddenFilter.userIds &&
            tableController.filter.data.assigneeType === 'user'
        "
        [items]="users"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_users' | translate }}"
        [(ngModel)]="tableController.filter.data.userIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <ng-container
        *ngIf="
            !hiddenFilter.departmentIds &&
            tableController.filter.data.assigneeType === 'department'
        "
    >
        <!-- Departments -->
        <ng-select
            [items]="departments"
            bindValue="id"
            bindLabel="name"
            [multiple]="true"
            placeholder="{{ 'translate_departments' | translate }}"
            [(ngModel)]="tableController.filter.data.departmentIds"
            (change)="tableController.filter$.next(true)"
        >
        </ng-select>

        <!-- include Sub Departments -->
        <div>
            <label class="flex flex-row items-center gap-2">
                <input
                    type="checkbox"
                    [(ngModel)]="
                        tableController.filter.data.includeChildDepartments
                    "
                    (change)="tableController.filter$.next(true)"
                />
                <span>
                    {{ 'translate_include_child_departments' | translate }}
                </span>
            </label>
        </div>
    </ng-container>

    <!-- Status -->
    <ng-select
        [items]="[
            {
                id: 'completed',
                name: ('translate_completed' | translate)
            },

            {
                id: 'inProgress',
                name: ('translate_in_progress' | translate)
            }
        ]"
        bindValue="id"
        bindLabel="name"
        placeholder="{{ 'translate_procedure_status' | translate }}"
        [clearable]="true"
        [searchable]="false"
        [(ngModel)]="tableController.filter.data.status"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>
</app-filter-result-box>

<!-- Table -->
<app-list-loading [items]="tableController.items">
    <table>
        <thead>
            <tr>
                <th></th>
                <th *ngIf="!hiddenColumns.name">
                    <app-table-order-column
                        label="translate_procedure_name"
                        orderByColumnName="name"
                        [tableController]="tableController"
                    ></app-table-order-column>
                </th>
                <th *ngIf="!hiddenColumns.task">
                    <app-table-order-column
                        label="translate_task"
                        orderByColumnName="task.name"
                        [tableController]="tableController"
                    ></app-table-order-column>
                </th>
                <th *ngIf="!hiddenColumns.plan">
                    <app-table-order-column
                        label="translate_plan"
                        orderByColumnName="task.plan.name"
                        [tableController]="tableController"
                    ></app-table-order-column>
                </th>
                <th *ngIf="!hiddenColumns.assigned">
                    {{ 'translate_assigned' | translate }}
                </th>
                <th *ngIf="!hiddenColumns.from">
                    <app-table-order-column
                        label="translate_from"
                        orderByColumnName="from"
                        [tableController]="tableController"
                    ></app-table-order-column>
                </th>
                <th *ngIf="!hiddenColumns.to">
                    <app-table-order-column
                        label="translate_to"
                        orderByColumnName="to"
                        [tableController]="tableController"
                    ></app-table-order-column>
                </th>
                <th *ngIf="!hiddenColumns.weight">
                    <app-table-order-column
                        label="translate_weight"
                        orderByColumnName="weight"
                        [tableController]="tableController"
                    ></app-table-order-column>
                </th>
                <th *ngIf="!hiddenColumns.isApproved">
                    {{ 'translate_is_done_qm' | translate }}
                </th>
                <th
                    *appHasPermissionId="permissionList.planWrite"
                    style="width: 0"
                >
                    <i class="fa-light fa-gear"></i>
                </th>
            </tr>
        </thead>

        <tbody>
            <tr *ngFor="let item of tableController.items; let idx = index">
                <!-- Index -->
                <td class="text-center">
                    {{
                        tableController.filter.pageNumber *
                            tableController.filter.pageSize +
                            idx +
                            1
                    }}
                </td>

                <!-- Name -->
                <td *ngIf="!hiddenColumns.name">
                    <a
                        class="cursor-pointer"
                        (click)="showPlanSubTaskDialog(item.id)"
                    >
                        {{ item.name }}
                    </a>
                </td>

                <!-- Task -->
                <td *ngIf="!hiddenColumns.task">
                    <a [routerLink]="['', 'plan-task', 'detail', item.task.id]">
                        {{ item.task.name }}
                    </a>
                </td>

                <!-- Plan -->
                <td *ngIf="!hiddenColumns.plan">
                    <a [routerLink]="['', 'plan', 'detail', item.task.plan.id]">
                        {{ item.task.plan.name }}
                    </a>
                </td>

                <!-- Assigned -->
                <td *ngIf="!hiddenColumns.assigned">
                    <app-assigned-entity [item]="item"></app-assigned-entity>
                </td>

                <!-- From -->
                <td *ngIf="!hiddenColumns.from" class="whitespace-nowrap">
                    {{ item.from | date : 'yyyy-MM-dd' }}
                </td>

                <!-- To -->
                <td *ngIf="!hiddenColumns.to" class="whitespace-nowrap">
                    {{ item.to | date : 'yyyy-MM-dd' }}
                </td>

                <!-- Weight -->
                <td *ngIf="!hiddenColumns.weight">
                    {{ item.weight * 100 | round : 2 }}%
                </td>

                <!-- Is approved -->
                <td *ngIf="!hiddenColumns.isApproved" class="text-center">
                    <i
                        class="fa-light"
                        [ngClass]="{
                            'fa-check': item.isApproved,
                            'fa-hourglass-clock': !item.isApproved
                        }"
                    ></i>
                </td>

                <!-- Links & Buttons -->
                <td
                    class="whitespace-nowrap"
                    *appHasPermissionId="permissionList.planWrite"
                >
                    <app-dropdown>
                        <!-- Edit -->
                        <a
                            class="btn btn-sm btn-info"
                            *ngIf="item.planUserAbility.canEdit"
                            [routerLink]="['', 'plan-subtask', 'edit', item.id]"
                        >
                            <i class="fa-light fa-edit fa-fw"></i>
                        </a>

                        <!-- Delete -->
                        <button
                            *ngIf="item.planUserAbility.canEdit"
                            [disabled]="currentlyDeleting.includes(item.id)"
                            class="btn btn-sm btn-danger"
                            (confirm)="delete(item)"
                            [swal]="{
                                title:
                                    'translate_delete_this_item_question_mark'
                                    | translate,
                                confirmButtonText: 'translate_yes' | translate,
                                cancelButtonText:
                                    'translate_cancel' | translate,
                                showCancelButton: true,
                                showCloseButton: true
                            }"
                        >
                            <i class="fas fa-trash fa-fw"></i>
                        </button>
                    </app-dropdown>
                </td>
            </tr>
        </tbody>
    </table>
    <app-table-pagination
        [tableController]="tableController"
    ></app-table-pagination>
</app-list-loading>
