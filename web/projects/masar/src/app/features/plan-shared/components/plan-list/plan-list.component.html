<!-- Filter -->
<app-filter-result-box>
    <!-- Keyword -->
    <app-search-input
        *ngIf="shownFilters.keyword"
        [placeholder]="'translate_search_by_plan_title'"
        [(ngModel)]="tableController.filter.data.keyword"
        [tableController]="tableController"
    ></app-search-input>

    <!-- Years -->
    <ng-select
        *ngIf="shownFilters.years"
        [items]="years"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_years' | translate }}"
        [(ngModel)]="tableController.filter.data.years"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Progress Status -->
    <ng-select
        *ngIf="shownFilters.progressStatus"
        [items]="progressStatuses"
        bindValue="id"
        bindLabel="name"
        placeholder="{{ 'translate_progress' | translate }}"
        [searchable]="false"
        [(ngModel)]="tableController.filter.data.progressStatus"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Categories -->
    <ng-select
        *ngIf="shownFilters.categoryIds"
        [items]="categories"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_categories' | translate }}"
        [(ngModel)]="tableController.filter.data.categoryIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- From -->
    <div *ngIf="shownFilters.from" class="flex flex-row items-center">
        <input
            class="flex-grow"
            #from="appFlatpickr"
            type="date"
            appFlatpickr
            [(ngModel)]="tableController.filter.data.from"
            (flatPickrChange)="tableController.filter$.next(true)"
            placeholder="{{ 'translate_from' | translate }}"
        />
        <button (click)="from.clear()" class="btn btn-sm btn-primary ms-1">
            <i class="fa-light fa-times"></i>
        </button>
    </div>

    <!-- To -->
    <div *ngIf="shownFilters.to" class="flex flex-row items-center">
        <input
            class="flex-grow"
            #to="appFlatpickr"
            type="date"
            appFlatpickr
            [(ngModel)]="tableController.filter.data.to"
            (flatPickrChange)="tableController.filter$.next(true)"
            placeholder="{{ 'translate_to' | translate }}"
        />
        <button (click)="to.clear()" class="btn btn-sm btn-primary ms-1">
            <i class="fa-light fa-times"></i>
        </button>
    </div>

    <!-- Government Strategic Goals -->
    <ng-select
        *ngIf="shownFilters.governmentStrategicGoalIds"
        [items]="governmentStrategicGoals"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_government_strategic_goal' | translate }}"
        [(ngModel)]="tableController.filter.data.governmentStrategicGoalIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Ministry Strategic Goals -->
    <ng-select
        *ngIf="shownFilters.strategicGoalIds"
        [items]="strategicGoals"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_ministry_strategic_goals' | translate }}"
        [(ngModel)]="tableController.filter.data.strategicGoalIds"
        (change)="tableController.filter$.next(true)"
        groupBy="yearRange"
    >
    </ng-select>

    <!-- Kpis -->
    <ng-select
        *ngIf="shownFilters.kpiIds"
        [items]="kpiLoader.items$ | async"
        [typeahead]="kpiLoader.itemInput$"
        [loading]="kpiLoader.itemsLoading"
        (open)="kpiLoader.loadInitialList()"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_national_indicators' | translate }}"
        [(ngModel)]="tableController.filter.data.kpiIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Partners -->
    <ng-select
        *ngIf="shownFilters.partnerIds"
        [items]="partners"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_partners' | translate }}"
        [(ngModel)]="tableController.filter.data.partnerIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Initiatives -->
    <input
        *ngIf="shownFilters.initiatives"
        type="text"
        placeholder="{{ 'translate_search_by_initiatives' | translate }}"
        [(ngModel)]="tableController.filter.data.initiatives"
        (keyup)="tableController.filter$.next()"
    />

    <!-- Status -->
    <ng-select
        *ngIf="mode !== 'not_approved' && shownFilters.status"
        [items]="[
            {
                id: '',
                name: ('translate_all' | translate)
            },
            {
                id: 'new',
                name: ('translate_approval_status_new' | translate)
            },

            {
                id: 'approved',
                name: ('translate_approval_status_approved' | translate)
            }
        ]"
        bindValue="id"
        bindLabel="name"
        placeholder="{{ 'translate_approval_status' | translate }}"
        [clearable]="false"
        [searchable]="false"
        [(ngModel)]="tableController.filter.data.status"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <div *ngIf="shownFilters.partneringDepartmentIds && isInternalPartner">
        <ng-select
            *ngIf="shownFilters.departmentIds"
            [items]="departments"
            bindLabel="name"
            [multiple]="true"
            placeholder="{{ 'translate_internal_partners' | translate }}"
            [(ngModel)]="tableController.filter.data.partneringDepartmentIds"
            (change)="tableController.filter$.next(true)"
            [groupBy]="'parentDepartment.name'"
        >
            <ng-template ng-option-tmp let-item="item">
                <div class="flex flex-col">
                    <span class="font-medium">{{ item.name }}</span>
                    <div
                        class="text-sm text-gray-500"
                        *ngIf="
                            item.parentDepartment?.name ||
                            item.secondParentDepartment?.name ||
                            item.thirdParentDepartment?.name
                        "
                    >
                        <div
                            class="block font-semibold"
                            *ngIf="item.parentDepartment?.name"
                        >
                            {{ item.parentDepartment.name }}
                        </div>
                        <div
                            class="block"
                            *ngIf="item.secondParentDepartment?.name"
                        >
                            {{ item.secondParentDepartment.name }}
                        </div>
                    </div>
                </div>
            </ng-template>

            <ng-template ng-label-tmp let-item="item">
                <div class="flex flex-col">
                    <span class="font-medium">{{ item.name }}</span>
                </div>
            </ng-template>

            <ng-template ng-optgroup-tmp let-item="item" let-index="index">
                <span class="font-bold text-gray-700">{{ item }}</span>
            </ng-template>
        </ng-select>
    </div>

    <!-- Assignee Type -->
    <ng-select
        *ngIf="shownFilters.assigneeType"
        [items]="assigneeTypes"
        bindValue="id"
        bindLabel="name"
        [placeholder]="'translate_assignee_type' | translate"
        [(ngModel)]="tableController.filter.data.assigneeType"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Departments -->
    <ng-select
        *ngIf="
            tableController.filter.data.assigneeType === 'department' &&
            shownFilters.departmentIds
        "
        [items]="departments"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_departments' | translate }}"
        [(ngModel)]="tableController.filter.data.departmentIds"
        (change)="tableController.filter$.next(true)"
        [groupBy]="'parentDepartment.name'"
    >
        <ng-template ng-option-tmp let-item="item">
            <div class="flex flex-col">
                <span class="font-medium">{{ item.name }}</span>
                <div
                    class="text-sm text-gray-500"
                    *ngIf="
                        item.parentDepartment?.name ||
                        item.secondParentDepartment?.name ||
                        item.thirdParentDepartment?.name
                    "
                >
                    <div
                        class="block font-semibold"
                        *ngIf="item.parentDepartment?.name"
                    >
                        {{ item.parentDepartment.name }}
                    </div>
                    <div
                        class="block"
                        *ngIf="item.secondParentDepartment?.name"
                    >
                        {{ item.secondParentDepartment.name }}
                    </div>
                </div>
            </div>
        </ng-template>

        <ng-template ng-label-tmp let-item="item">
            <div class="flex flex-col">
                <span class="font-medium">{{ item.name }}</span>
            </div>
        </ng-template>

        <ng-template ng-optgroup-tmp let-item="item" let-index="index">
            <span class="font-bold text-gray-700">{{ item }}</span>
        </ng-template>
    </ng-select>

    <!-- Teams -->
    <ng-select
        *ngIf="
            tableController.filter.data.assigneeType === 'team' &&
            shownFilters.teamIds
        "
        [items]="teams"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_teams' | translate }}"
        [(ngModel)]="tableController.filter.data.teamIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Users -->
    <ng-select
        *ngIf="
            tableController.filter.data.assigneeType === 'user' &&
            shownFilters.userIds
        "
        [items]="users"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_users' | translate }}"
        [(ngModel)]="tableController.filter.data.userIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- include Sub Departments -->
    <div
        *ngIf="
            tableController.filter.data.assigneeType === 'department' &&
            shownFilters.includeChildDepartments
        "
    >
        <label class="flex flex-row items-center gap-2">
            <input
                type="checkbox"
                [(ngModel)]="
                    tableController.filter.data.includeChildDepartments
                "
                (change)="tableController.filter$.next(true)"
            />
            <span>
                {{ 'translate_include_child_departments' | translate }}
            </span>
        </label>
    </div>
</app-filter-result-box>

<!-- Table -->
<app-list-loading [items]="tableController.items">
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th *ngIf="mode !== 'not_approved'"></th>
                    <th *ngIf="mode !== 'not_approved' && shownFields.progress">
                        {{ 'translate_progress' | translate }}
                    </th>
                    <th *ngIf="shownFields.serialNumber">
                        {{ 'translate_serial_number' | translate }}
                    </th>
                    <th *ngIf="shownFields.planTitle">
                        <app-table-order-column
                            label="translate_plan_title"
                            orderByColumnName="name"
                            [tableController]="tableController"
                        ></app-table-order-column>
                    </th>
                    <th *ngIf="shownFields.assigned">
                        {{ 'translate_assigned' | translate }}
                    </th>
                    <th
                        *ngIf="
                            mode === 'not_approved' &&
                            shownFields.approvingDepartment
                        "
                    >
                        {{ 'translate_approving_department' | translate }}
                    </th>
                    <th *ngIf="shownFields.from">
                        <app-table-order-column
                            label="translate_from"
                            orderByColumnName="from"
                            [tableController]="tableController"
                        ></app-table-order-column>
                    </th>
                    <th *ngIf="shownFields.to">
                        <app-table-order-column
                            label="translate_to"
                            orderByColumnName="to"
                            [tableController]="tableController"
                        ></app-table-order-column>
                    </th>
                    <th
                        *ngIf="
                            mode !== 'not_approved' &&
                            shownFields.approvalStatus
                        "
                    >
                        {{ 'translate_approval_status' | translate }}
                    </th>
                    <th *ngIf="shownFields.actions" style="width: 0">
                        <i class="fa-light fa-gear"></i>
                    </th>
                </tr>
            </thead>

            <tbody>
                <tr
                    *ngFor="let item of tableController.items; let idx = index"
                    [ngClass]="{ 'bg-gray-100': item.pinned }"
                >
                    <!-- Progress indicator -->
                    <td
                        *ngIf="mode !== 'not_approved'"
                        [ngClass]="{
                            'bg-gray-400': item.progress === null,
                            'bg-red-500':
                                item.progress !== null && item.progress < 0.6,
                            'bg-yellow-500':
                                0.6 <= item.progress && item.progress < 0.9,
                            'bg-green-500': 0.9 <= item.progress
                        }"
                    ></td>

                    <!-- Progress -->
                    <td
                        *ngIf="mode !== 'not_approved' && shownFields.progress"
                        class="text-center"
                    >
                        {{ item.progress * 100 | round : 2 }}%
                    </td>

                    <!-- Serial number -->
                    <td *ngIf="shownFields.serialNumber">
                        {{ item.code + '/' + item.year }}
                    </td>

                    <!-- Title -->
                    <td *ngIf="shownFields.planTitle">
                        <a [routerLink]="['', 'plan', 'detail', item.id]">
                            {{ item.name }}
                        </a>
                    </td>

                    <!-- Assigned -->
                    <td *ngIf="shownFields.assigned">
                        <app-assigned-entity
                            [item]="item"
                        ></app-assigned-entity>
                    </td>

                    <!-- Current approving department -->
                    <td
                        *ngIf="
                            mode === 'not_approved' &&
                            shownFields.approvingDepartment
                        "
                    >
                        <ng-container
                            *ngIf="
                                item.currentApprovingDepartment;
                                else planAdministration
                            "
                        >
                            <a
                                *appHasPermissionId="
                                    permissionList.departmentRead;
                                    else justNameTemplateRef
                                "
                                [routerLink]="[
                                    '',
                                    'department',
                                    'detail',
                                    item.currentApprovingDepartment.id
                                ]"
                            >
                                {{ item.currentApprovingDepartment.name }}
                            </a>
                            <ng-template #justNameTemplateRef>
                                {{ item.currentApprovingDepartment.name }}
                            </ng-template>
                        </ng-container>
                        <ng-template #planAdministration>
                            {{ 'translate_final_approval' | translate }}
                        </ng-template>
                    </td>

                    <!-- From -->
                    <td *ngIf="shownFields.from" class="whitespace-nowrap">
                        {{ item.from | date : 'yyyy-MM-dd' }}
                    </td>

                    <!-- To -->
                    <td *ngIf="shownFields.to" class="whitespace-nowrap">
                        {{ item.to | date : 'yyyy-MM-dd' }}
                    </td>

                    <!-- Is approved -->
                    <td
                        *ngIf="
                            mode !== 'not_approved' &&
                            shownFields.approvalStatus
                        "
                        class="text-center"
                    >
                        {{
                            'translate_flow_state_' + item.flowState | translate
                        }}
                    </td>

                    <!-- Links & Buttons -->
                    <td *ngIf="shownFields.actions" class="whitespace-nowrap">
                        <!-- Flow -->
                        <app-dropdown>
                            <!-- Flow Move Button -->
                            <ng-container
                                *appHasPermissionId="
                                    permissionList.fullAccess;
                                    else regularUserButton
                                "
                            >
                                <app-flow-move-button
                                    itemType="plan"
                                    [item]="item"
                                    (transfer)="tableController.filter$.next()"
                                ></app-flow-move-button>
                            </ng-container>

                            <!-- Template for regular users that will be shown when fullAccess permission is not present -->
                            <ng-template #regularUserButton>
                                <!-- Flow Move Button -->
                                <app-flow-move-button
                                    *ngIf="
                                        !shouldRuleCheckBeforeSubmission ||
                                            item.isRuleCheckPassed;
                                        else checkNotPassedTemplate
                                    "
                                    itemType="plan"
                                    [item]="item"
                                    (transfer)="tableController.filter$.next()"
                                ></app-flow-move-button>

                                <!-- No Actions Available Button -->
                                <ng-template #checkNotPassedTemplate>
                                    <button
                                        class="btn btn-sm btn-primary"
                                        [appTooltip]="
                                            'translate_no_actions_available'
                                                | translate
                                        "
                                        [swal]="{
                                            title:
                                                'translate_no_actions_available'
                                                | translate,
                                            html:
                                                'translate_action_not_available_message'
                                                | translate,
                                            icon: 'info',
                                            confirmButtonText:
                                                'translate_ok' | translate,
                                            showCancelButton: false,
                                            showCloseButton: true
                                        }"
                                    >
                                        <i class="fa fa-ban fa-fw"></i>
                                        <span *ngIf="showLabel">
                                            {{
                                                'translate_no_actions_available'
                                                    | translate
                                            }}
                                        </span>
                                    </button>
                                </ng-template>
                            </ng-template>
                            <!-- Duplicate -->
                            <button
                                *appHasPermissionId="permissionList.planWrite"
                                [disabled]="
                                    currentlyProcessing.includes(item.id)
                                "
                                (confirm)="duplicate(item)"
                                [swal]="{
                                    title:
                                        'translate_duplicate_this_item_qm'
                                        | translate,
                                    confirmButtonText:
                                        'translate_yes' | translate,
                                    cancelButtonText:
                                        'translate_cancel' | translate,
                                    showCancelButton: true,
                                    showCloseButton: true
                                }"
                                class="btn btn-sm btn-warning"
                                [appTooltip]="'translate_duplicate' | translate"
                            >
                                <i class="fas fa-copy fa-fw"></i>
                            </button>

                            <ng-container *ngIf="mode !== 'not_approved'">
                                <!-- Export -->
                                <button
                                    class="btn btn-sm btn-info"
                                    (click)="export(item)"
                                    [disabled]="
                                        currentlyProcessing.includes(item.id)
                                    "
                                    [appTooltip]="
                                        'translate_export' | translate
                                    "
                                >
                                    <i class="fa-light fa-download fa-fw"></i>
                                </button>

                                <!-- Gantt -->
                                <button
                                    class="btn btn-sm btn-success"
                                    (click)="showGanttDialog(item)"
                                    [disabled]="
                                        currentlyProcessing.includes(item.id)
                                    "
                                    [appTooltip]="'translate_graph' | translate"
                                >
                                    <i
                                        class="fa-light fa-chart-gantt fa-fw"
                                    ></i>
                                </button>

                                <!-- Edit -->
                                <a
                                    class="btn btn-sm btn-info"
                                    *appHasPermissionId="
                                        permissionList.planWrite
                                    "
                                    [routerLink]="['', 'plan', 'edit', item.id]"
                                    [appTooltip]="'translate_edit' | translate"
                                >
                                    <i class="fa-light fa-edit fa-fw"></i>
                                </a>

                                <!-- Delete -->
                                <button
                                    *appHasPermissionId="
                                        permissionList.planDelete
                                    "
                                    class="btn btn-sm btn-danger"
                                    [disabled]="
                                        currentlyProcessing.includes(item.id)
                                    "
                                    (confirm)="delete(item)"
                                    [swal]="{
                                        title:
                                            'translate_delete_this_item_question_mark'
                                            | translate,
                                        confirmButtonText:
                                            'translate_yes' | translate,
                                        cancelButtonText:
                                            'translate_cancel' | translate,
                                        showCancelButton: true,
                                        showCloseButton: true
                                    }"
                                    [appTooltip]="
                                        'translate_delete' | translate
                                    "
                                >
                                    <i class="fas fa-trash fa-fw"></i>
                                </button>
                                <!-- Toggle pin -->
                                <button
                                    *appHasPermissionId="
                                        permissionList.fullAccess
                                    "
                                    class="btn btn-sm"
                                    [ngClass]="{
                                        'btn-black': item.pinned,
                                        'btn-outline-black': !item.pinned
                                    }"
                                    (click)="togglePin(item)"
                                    [disabled]="
                                        currentlyProcessing.includes(item.id)
                                    "
                                    [appTooltip]="
                                        (item.pinned
                                            ? 'translate_remove_as_pin'
                                            : 'translate_set_as_pin'
                                        ) | translate
                                    "
                                >
                                    <i
                                        class="fa-fw fa-thumb-tack"
                                        [ngClass]="{
                                            'fa-light': !item.pinned,
                                            'fa-solid': item.pinned
                                        }"
                                    ></i>
                                </button>
                            </ng-container>
                        </app-dropdown>
                    </td>
                </tr>
            </tbody>
        </table>
        <app-table-pagination
            [tableController]="tableController"
        ></app-table-pagination>
    </div>
</app-list-loading>
