import {
    AfterViewInit,
    Component,
    ElementRef,
    Input,
    ViewChild,
} from '@angular/core';
import Gantt from 'mnm-frappe-gantt';
import { TranslateService } from '@ngx-translate/core';
import { finalize } from 'rxjs/operators';
import { PlanService } from '@masar/pages/plan/plan.service';

@Component({
    selector: 'app-plan-gantt',
    templateUrl: './plan-gantt.component.html',
})
export class PlanGanttComponent implements AfterViewInit {
    @Input() public planId: string;
    @ViewChild('ganttElm') private ganttElmRef: ElementRef;

    public isLoading = true;

    public constructor(
        private planService: PlanService,
        private translateService: TranslateService
    ) {}

    public ngAfterViewInit(): void {
        this.planService
            .planForGantt(this.planId)
            .pipe(finalize(() => (this.isLoading = false)))
            .subscribe(plan => {
                const tasks = [];

                plan.tasks.forEach(task => {
                    tasks.push({
                        id: task.id,
                        name: task.name,
                        start: new Date(task.from),
                        end: new Date(task.to),
                        progress: task.progress * 100,
                        dependencies: '',
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        custom_class: 'bar-task', // optional
                    });

                    task.subtasks.forEach(subtask => {
                        tasks.push({
                            id: subtask.id,
                            name: subtask.name,
                            start: new Date(subtask.from),
                            end: new Date(subtask.to),
                            progress: subtask.progress * 100,
                            dependencies: task.id,
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            custom_class: 'bar-subtask', // optional
                        });

                        subtask.subsubtasks.forEach((subsubtask, idx) => {
                            tasks.push({
                                id: subsubtask.id,
                                name: this.translateService.instant(
                                    'translate_period_period',
                                    {
                                        period: idx + 1,
                                    }
                                ),
                                start: new Date(subsubtask.from),
                                end: new Date(subsubtask.to),
                                progress:
                                    subsubtask.status === 'approved' ? 100 : 0,
                                dependencies: subtask.id,
                                // eslint-disable-next-line @typescript-eslint/naming-convention
                                custom_class: 'bar-subsubtask', // optional
                            });
                        });
                    });
                });

                if (tasks.length === 0) {
                    this.ganttElmRef.nativeElement.textContent =
                        this.translateService.instant(
                            'translate_no_items_were_found'
                        );

                    return;
                }

                new Gantt(this.ganttElmRef.nativeElement, tasks, {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    is_editable: false,
                });
            });
    }
}
