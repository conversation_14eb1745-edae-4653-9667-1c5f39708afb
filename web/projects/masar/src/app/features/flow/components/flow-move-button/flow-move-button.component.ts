import {
    Component,
    EventEmitter,
    HostBinding,
    Input,
    NgModuleRef,
    OnChanges,
    Output,
    Renderer2,
    SimpleChanges,
} from '@angular/core';
import { ModalService } from 'mnm-webapp';
import { FlowMoveFormComponent } from '@masar/features/flow/components';
import { extractAvailableStates } from '@masar/features/flow/utils/extract-available-states';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FlowItem } from '@masar/features/flow/interfaces';
import { FlowItemType } from '@masar/features/flow/types';

@Component({
    selector: 'app-flow-move-button',
    templateUrl: './flow-move-button.component.html',
})
export class FlowMoveButtonComponent implements OnChanges {
    @Input() public item: FlowItem;

    @Input() public itemType: FlowItemType;

    @Input() public showLabel: boolean = false;

    @Output() public transfer = new EventEmitter<string>();

    public areThereAvailableActions = false;

    public constructor(
        private moduleRef: NgModuleRef<any>,
        private modalService: ModalService,
        private readonly renderer: Renderer2,
        private translateService: TranslateService
    ) {}

    public ngOnChanges(_: SimpleChanges): void {
        this.areThereAvailableActions =
            extractAvailableStates(this.item).length > 0;
    }

    public async showForm(): Promise<void> {
        const subject = new Subject();

        const component = await this.modalService.show(FlowMoveFormComponent, {
            moduleRef: this.moduleRef,
            title: this.translateService.instant(
                'translate_update_request_status'
            ),
            beforeInit: c => {
                this.renderer.addClass(document.body, 'modal-no-overlay');
                c.item = this.item;
                c.itemType = this.itemType;
            },
            onDismiss: () => {
                subject.next();
                subject.complete();

                // TODO: Add option in mnm-webapp library to add custom class to the modal container via ModalOptions
                this.renderer.removeClass(document.body, 'modal-no-overlay');
            },
        });

        component.transfer.pipe(takeUntil(subject)).subscribe(state => {
            this.transfer.emit(state);
            this.modalService.dismiss(component);
        });
    }

    @HostBinding('class.hidden')
    protected get className(): boolean {
        return !this.areThereAvailableActions;
    }
}
