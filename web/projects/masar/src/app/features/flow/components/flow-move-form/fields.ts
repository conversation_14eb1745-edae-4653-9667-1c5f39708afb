import { MnmFormField } from '@masar/shared/components';
import { Validators } from '@angular/forms';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'state',
        label: 'translate_move_to',
        type: 'select',
        bindValue: 'id',
        bindLabel: 'name',
        validators: [Validators.required],
    },

    {
        name: 'note',
        label: 'translate_notes',
        type: 'textarea',
    },
];
