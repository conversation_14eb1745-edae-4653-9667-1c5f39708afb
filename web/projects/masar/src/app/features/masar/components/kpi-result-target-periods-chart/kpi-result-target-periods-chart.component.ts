import {
    AfterViewInit,
    Component,
    ElementRef,
    Inject,
    Input,
    OnChanges,
    OnDestroy,
    ViewChild,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import * as Chart from 'chart.js';
import { Subject } from 'rxjs';
import { functions } from '@masar/common/misc/functions';
import { Item, Kpi, KpiResultPeriod } from '@masar/common/models';
import { DOCUMENT } from '@angular/common';
import 'chartjs-plugin-labels';
import { AppSettingFetcherService, MiscApiService } from '@masar/core/services';
import { takeUntil } from 'rxjs/operators';
import { getMostFrequentItem, getDistinctItems } from '@masar/common/utils';
import { KpiMeasurementCycleType } from '@masar/common/types';

@Component({
    selector: 'app-kpi-result-target-periods-chart',
    templateUrl: './kpi-result-target-periods-chart.component.html',
    styles: [
        `
            :host {
                display: block;
                width: 100%;
                height: 100%;
            }
        `,
    ],
})
export class KpiResultTargetPeriodsChartComponent
    implements AfterViewInit, OnChanges, OnDestroy
{
    @Input() public kpi: Kpi;

    // Checks if the result is owned by the department
    // before displaying the result on the graph. Should
    // be set to false if the kpi has only a single result
    // per year that represents the overall result of
    // the kpi.
    @Input() public checkIsOwning: boolean = true;

    @ViewChild('canvasRef')
    private canvasRef: ElementRef<HTMLCanvasElement>;

    public years: number[] = [];

    public periods: Item[] = [];

    public allMeasurementCycles: Item[] = [];

    public measurementCycles: Item[] = [];

    public selectedPeriods: Item[] = [];

    public selectedYear?: number;

    public selectedMeasurementCycleId?: KpiMeasurementCycleType;

    private chart: Chart;
    private sortedResultsPeriods: KpiResultPeriod[];
    private unsubscribeAll = new Subject();
    private isGraphTicksEnabled = false;

    public constructor(
        private translateService: TranslateService,
        private appSettingFetcherService: AppSettingFetcherService,
        @Inject(DOCUMENT) private document: Document,
        private readonly miscApiService: MiscApiService
    ) {
        this.miscApiService.getList('kpi-cycle').subscribe(items => {
            this.allMeasurementCycles = items;
            this.onChangeKpi();
        });
    }

    public ngAfterViewInit(): void {
        // noinspection JSUnusedGlobalSymbols
        this.chart = new Chart(this.canvasRef.nativeElement.getContext('2d'), {
            type: 'bar',
            options: {
                scales: {
                    yAxes: [
                        {
                            display: true,
                            ticks: {
                                suggestedMin: 0,
                            },
                        },
                    ],
                },
                legend: {
                    display: false,
                },
            },
        });

        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(settings => {
                this.isGraphTicksEnabled =
                    settings.kpiSetting.isGraphTicksEnabled;
                this.toggleTicks();
            });

        this.onChangeKpi();
    }

    // When kpi value changes.
    public ngOnChanges(): void {
        this.onChangeKpi();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public redrawChart(): void {
        if (!this.kpi || !this.chart) {
            return;
        }

        const style = getComputedStyle(this.document.documentElement);
        const primaryColor = style.getPropertyValue('--primary-500');
        const secondaryColor = style.getPropertyValue('--secondary-500');

        let results = this.kpi.results
            .filter(x => x.year === this.selectedYear)
            .sort((x, y) => x.currentPeriod - y.currentPeriod);

        if (this.checkIsOwning) {
            results = results.filter(x => x.isOwningDepartment);
        }

        const periods = this.selectedPeriods?.length
            ? this.selectedPeriods
            : this.periods;

        const resultLabel = this.translateService.instant('translate_result');
        const targetLabel = this.translateService.instant('translate_target');

        const periodsResults = periods.map(p =>
            results.flatMap(r => r.periods).find(x => x.period === +p.id)
        );

        const dataset = [
            {
                label: resultLabel,
                data: periodsResults.map(p =>
                    functions.roundNumber(p.achieved * 100, 2)
                ),
                barThickness: 30,
                backgroundColor: secondaryColor,
                borderColor: secondaryColor,
                order: 1,
            },
        ];

        if (!this.kpi.isTrend) {
            dataset.push({
                label: targetLabel,
                data: periodsResults.map(p =>
                    functions.roundNumber(p.achieved * 100, 2)
                ),
                type: 'line',
                fill: false,
                borderColor: primaryColor,
                backgroundColor: primaryColor,
                pointBorderWidth: 10,
                order: 0,
            } as any);
        }

        this.chart.data.datasets = dataset;

        this.chart.data.labels = periods.map(x => x.name);
        this.sortedResultsPeriods = periodsResults;

        this.chart.options.scales.yAxes[0].ticks.suggestedMax =
            Math.max(...periodsResults.map(x => x.achieved * 100)) + 10;

        this.chart.update();
        this.toggleTicks();
    }

    public onChangeKpi(): void {
        if (!this.kpi || this.allMeasurementCycles.length === 0) return;
        this.updateYearList();
    }

    public updateMeasurementCycleList(): void {
        this.measurementCycles = [];

        if (this.selectedYear) {
            const allMeasurementCycles = this.kpi.results
                .filter(r => r.year === this.selectedYear)
                .map(x => x.measurementCycle);

            const measurementCycles =
                getDistinctItems(allMeasurementCycles).sort();

            this.measurementCycles = this.allMeasurementCycles.filter(x =>
                measurementCycles.includes(x.id as KpiMeasurementCycleType)
            );

            this.selectedMeasurementCycleId = getMostFrequentItem(
                this.measurementCycles
            )?.id as KpiMeasurementCycleType;
        }

        this.selectedPeriods = [];
        this.updatePeriodList();
    }

    public updatePeriodList(): void {
        this.periods = [];

        if (this.selectedMeasurementCycleId) {
            const periodName = this.getPeriodName();

            const periodsLength = this.kpi.results.find(
                x => x.measurementCycle === this.selectedMeasurementCycleId
            ).periods.length;

            this.periods = Array.from({ length: periodsLength }, (_, i) => ({
                id: `${i}`,
                name: `${periodName} ${i + 1}`,
            }));
        }

        this.redrawChart();
    }

    private updateYearList(): void {
        this.years = [];
        this.selectedYear = undefined;

        if (this.kpi) {
            const allYears = this.kpi.results.map(x => x.year);
            this.years = getDistinctItems(allYears).sort((x, y) => y - x);
            this.selectedYear = this.years[0];
        }

        this.selectedMeasurementCycleId = undefined;
        this.updateMeasurementCycleList();
    }

    private getPeriodName(
        measurementCycle = this.selectedMeasurementCycleId
    ): string {
        let periodName: string;

        switch (measurementCycle) {
            case 'month':
                periodName = 'translate_month';
                break;
            case 'quarter':
                periodName = 'translate_quarter';
                break;
            case 'semi_annual':
                periodName = 'translate_half_year';
                break;
            case 'annual':
                periodName = 'translate_year';
                break;
            default:
                periodName = '';
                break;
        }

        return this.translateService.instant(periodName);
    }

    private toggleTicks(): void {
        const compute = (args): number => {
            const { index } = args;
            const { type } = args.dataset;
            const previousAchieved =
                this.sortedResultsPeriods[index - 1]?.achieved ?? null;
            const currentAchieved =
                this.sortedResultsPeriods[index]?.achieved ?? null;

            if (
                type !== 'line' ||
                index === 0 ||
                previousAchieved === null ||
                currentAchieved === null ||
                (this.kpi?.isTrend ?? true)
            ) {
                return null;
            }

            return currentAchieved - previousAchieved;
        };

        const ticksPlugin = {
            render: args => {
                const change = compute(args);

                const symbol = change < 0 ? '▼' : change > 0 ? '▲' : '';

                return change === null || !this.isGraphTicksEnabled
                    ? ''
                    : `${functions.roundNumber(change * 100, 1)}%${symbol} `;
            },

            fontColor: args => {
                const change = compute(args);
                return change > 0
                    ? '#075c07'
                    : change < 0
                    ? '#a22e2e'
                    : '#000000';
            },
            fontStyle: 'bold',
            position: 'border',
            textMargin: 10,
        };

        this.chart.options.plugins = { labels: ticksPlugin };
        this.chart.update();
    }
}
