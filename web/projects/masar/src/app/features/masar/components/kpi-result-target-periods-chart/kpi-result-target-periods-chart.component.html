<div
    class="mb-4 grid grid-cols-1 gap-4 rounded-md bg-gray-100 px-4 py-4 md:grid-cols-3"
>
    <!-- Years -->
    <div class="flex-grow">
        <label class="mb-2 block font-bold text-gray-700" for="years">
            {{ 'translate_year' | translate }}
        </label>
        <ng-select
            id="years"
            [items]="years"
            placeholder="{{ 'translate_year' | translate }}"
            [(ngModel)]="selectedYear"
            (change)="updateMeasurementCycleList()"
            [clearable]="false"
        >
        </ng-select>
    </div>

    <!-- Measurement Cycles -->
    <div class="flex-grow">
        <label
            class="mb-2 block font-bold text-gray-700"
            for="measurement_cycles"
        >
            {{ 'translate_measurement_cycle' | translate }}
        </label>
        <ng-select
            id="measurement_cycles"
            bindLabel="name"
            bindValue="id"
            [items]="measurementCycles"
            placeholder="{{ 'translate_measurement_cycle' | translate }}"
            [(ngModel)]="selectedMeasurementCycleId"
            [clearable]="false"
            (change)="updatePeriodList()"
        >
        </ng-select>
    </div>

    <!-- Periods -->
    <div class="flex-grow">
        <label class="mb-2 block font-bold text-gray-700" for="periods">
            {{ 'translate_periods' | translate }}
        </label>
        <ng-select
            id="periods"
            [items]="periods"
            bindLabel="name"
            [multiple]="true"
            placeholder="{{ 'translate_all_periods' | translate }}"
            [(ngModel)]="selectedPeriods"
            (change)="redrawChart()"
        >
        </ng-select>
    </div>
</div>

<h5 class="text-center font-bold">
    {{ 'translate_achieved' | translate }}
</h5>

<canvas #canvasRef></canvas>
