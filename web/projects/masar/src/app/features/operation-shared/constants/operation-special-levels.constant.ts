import { OperationSpecialLevel } from '@masar/features/operation-shared/interfaces';
import { isOrigin } from '@masar/common/utils';
import { OrganizationOrigin } from '@masar/common/enums';

export const operationSpecialLevels: OperationSpecialLevel[] = [
    {
        level: 4,
        translateUpdateLevel: 'translate_update_fourth_level_details',
        translateLevel: 'translate_level_four_details',
        isEnabled: true,
    },
    {
        level: 5,
        translateUpdateLevel: 'translate_update_fifth_level_details',
        translateLevel: 'translate_level_five_details',
        isEnabled: isOrigin([
            OrganizationOrigin.police,
            OrganizationOrigin.injaz,
            OrganizationOrigin.localhost,
            OrganizationOrigin.staging,
        ]),
    },
];
