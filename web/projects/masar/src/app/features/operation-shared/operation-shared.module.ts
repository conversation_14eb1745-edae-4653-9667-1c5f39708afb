import { NgModule } from '@angular/core';
import { MnmWebappModule } from 'mnm-webapp';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@masar/shared/shared.module';
import { MasarModule } from '@masar/features/masar/masar.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { RouterModule } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { TranslationModule } from '@ng-omar/translation';
import { OperationListComponent } from './operation-list/operation-list.component';
import { OperationSharedService } from './operation-shared.service';
import { GetOperationSpecialLevelPipe } from './pipes';

@NgModule({
    imports: [
        CommonModule,
        TranslationModule,
        MnmWebappModule,
        SharedModule,
        MasarModule,
        FormsModule,
        ReactiveFormsModule,
        SweetAlert2Module,
        RouterModule,
        NgSelectModule,
        DragDropModule,
    ],
    declarations: [OperationListComponent, GetOperationSpecialLevelPipe],
    exports: [OperationListComponent, GetOperationSpecialLevelPipe],
    providers: [OperationSharedService],
})
export class OperationSharedModule {}
