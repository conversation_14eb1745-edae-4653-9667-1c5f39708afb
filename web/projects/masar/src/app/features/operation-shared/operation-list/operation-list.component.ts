import {
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { permissionList } from '@masar/common/constants';
import { TableController } from '@masar/common/misc/table';
import { Operation, Item } from '@masar/common/models';
import { MiscApiService } from '@masar/core/services';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { OperationSharedService } from '../operation-shared.service';

type ShownFilters =
    | 'keyword'
    | 'code'
    | 'level'
    | 'departmentIds'
    | 'version'
    | 'strategyGoalIds';

type ShownFields =
    | 'code'
    | 'name'
    | 'weight'
    | 'level'
    | 'version'
    | 'ownerDepartment'
    | 'lastEnhancementDate'
    | 'actions';

interface FilterData {
    keyword?: string;
    code?: string;
    level?: string | null;
    departmentIds?: string[];
    version?: string;
    strategyGoalIds?: string[];
    includeChildDepartments?: boolean;
}

@Component({
    selector: 'app-operation-list',
    templateUrl: './operation-list.component.html',
})
export class OperationListComponent implements OnDestroy, OnInit {
    @Input() public filterByStrategicGoalId?: string;

    @Input() public isFilterInUrl = false;

    @Input() public shownFilters: Record<ShownFilters, boolean> = {
        keyword: true,
        code: true,
        level: true,
        departmentIds: true,
        version: true,
        strategyGoalIds: true,
    };

    @Input() public shownFields: Record<ShownFields, boolean> = {
        code: true,
        name: true,
        weight: true,
        level: true,
        version: true,
        ownerDepartment: true,
        lastEnhancementDate: true,
        actions: true,
    };

    @Output() public tableControllerChange = new EventEmitter<
        TableController<Operation, FilterData>
    >();

    public tableController: TableController<Operation, FilterData>;

    public currentlyDeleting: string[] = [];

    public permissionList = permissionList;

    public departments: Item[];

    public strategyGoals: any[];

    public constructor(
        private readonly operationSharedService: OperationSharedService,
        private readonly notificationService: NotificationService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly miscApiService: MiscApiService,
        private readonly router: Router
    ) {}

    public ngOnInit(): void {
        this.tableController = new TableController<Operation, FilterData>(
            filter => {
                return this.operationSharedService.list(
                    filter.data.keyword,
                    filter.data.code,
                    filter.data.level,
                    filter.data.departmentIds,
                    filter.data.includeChildDepartments,
                    filter.data.version,
                    this.filterByStrategicGoalId
                        ? [this.filterByStrategicGoalId]
                        : filter.data.strategyGoalIds,
                    filter.pageNumber,
                    filter.pageSize
                );
            },
            {
                data: {
                    keyword: '',
                    code: '',
                    level:
                        this.activatedRoute.snapshot.queryParams['level'] ??
                        null,
                    departmentIds: null,
                    version: '',
                    strategyGoalIds: null,
                },
            },
            this.isFilterInUrl
                ? {
                      routingControls: {
                          router: this.router,
                          activatedRoute: this.activatedRoute,
                      },
                  }
                : {}
        );

        this.tableController.start();

        this.tableControllerChange.emit(this.tableController);

        this.miscApiService
            .departments()
            .subscribe(items => (this.departments = items));

        this.miscApiService
            .getList('strategic-goal', undefined, true)
            .subscribe(items => (this.strategyGoals = items));
    }

    public delete(item: Operation): void {
        this.currentlyDeleting.push(item.id);
        this.operationSharedService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }
}
