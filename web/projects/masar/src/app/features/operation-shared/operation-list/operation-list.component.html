<!-- Filter box-->
<app-filter-result-box>
    <!-- Code -->
    <app-search-input
        *ngIf="shownFilters.code"
        [placeholder]="'translate_operation_code'"
        [(ngModel)]="tableController.filter.data.code"
        [tableController]="tableController"
    ></app-search-input>

    <!-- Name-->
    <app-search-input
        *ngIf="shownFilters.keyword"
        [(ngModel)]="tableController.filter.data.keyword"
        [tableController]="tableController"
    ></app-search-input>

    <!-- operation version-->
    <app-search-input
        *ngIf="shownFilters.version"
        [placeholder]="'translate_operation_version'"
        [(ngModel)]="tableController.filter.data.version"
        [tableController]="tableController"
    ></app-search-input>

    <!-- Level -->
    <ng-select
        *ngIf="shownFilters.level"
        [items]="[1, 2, 3, 4]"
        bindValue="id"
        bindLabel="name"
        [multiple]="false"
        placeholder="{{ 'translate_operation_level' | translate }}"
        [(ngModel)]="tableController.filter.data.level"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Departments -->
    <ng-select
        *ngIf="shownFilters.departmentIds"
        [items]="departments"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_departments' | translate }}"
        [(ngModel)]="tableController.filter.data.departmentIds"
        (change)="tableController.filter$.next(true)"
    ></ng-select>

    <!-- include Sub Departments -->
    <div *ngIf="tableController.filter.data.departmentIds?.length">
        <label class="flex flex-row items-center gap-2">
            <input
                type="checkbox"
                [(ngModel)]="
                    tableController.filter.data.includeChildDepartments
                "
                (change)="tableController.filter$.next(true)"
            />
            <span>
                {{ 'translate_include_child_departments' | translate }}
            </span>
        </label>
    </div>

    <!-- Strategic Goals -->
    <ng-select
        *ngIf="shownFilters.strategyGoalIds"
        [items]="strategyGoals"
        bindValue="id"
        bindLabel="name"
        [multiple]="false"
        placeholder="{{ 'translate_strategic_goals' | translate }}"
        [(ngModel)]="tableController.filter.data.strategyGoalIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>
</app-filter-result-box>

<!-- Table -->
<app-list-loading [items]="tableController.items">
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th *ngIf="shownFields.code">
                        {{ 'translate_code' | translate }}
                    </th>
                    <th *ngIf="shownFields.name">
                        {{ 'translate_name' | translate }}
                    </th>
                    <th *ngIf="shownFields.weight">
                        {{ 'translate_weight' | translate }}
                    </th>
                    <th *ngIf="shownFields.level">
                        {{ 'translate_operation_level' | translate }}
                    </th>
                    <th *ngIf="shownFields.version">
                        {{ 'translate_operation_version' | translate }}
                    </th>
                    <th *ngIf="shownFields.ownerDepartment">
                        {{ 'translate_operation_owner' | translate }}
                    </th>
                    <th *ngIf="shownFields.lastEnhancementDate">
                        {{ 'translate_last_enhancement_date' | translate }}
                    </th>
                    <ng-container *ngIf="shownFields.actions">
                        <th
                            *appHasAnyPermissionId="[
                                permissionList.operationWrite,
                                permissionList.operationDelete
                            ]"
                            style="width: 0"
                        >
                            <i class="fa-light fa-gear"></i>
                        </th>
                    </ng-container>
                </tr>
            </thead>

            <tbody>
                <ng-container *ngFor="let item of tableController.items">
                    <tr [ngClass]="{ 'bg-gray-50': item.level === 1 }">
                        <td class="text-center" *ngIf="shownFields.code">
                            <span class="badge badge-primary">
                                OPR-{{ item.code }}
                            </span>
                        </td>
                        <td *ngIf="shownFields.name">
                            <a
                                [routerLink]="[
                                    '',
                                    'operation',
                                    'detail',
                                    item.id
                                ]"
                            >
                                {{ item.name }}
                            </a>
                        </td>
                        <td *ngIf="shownFields.weight">
                            {{ item.weight }}
                        </td>
                        <td *ngIf="shownFields.level">
                            {{
                                'translate_level_level'
                                    | translate : { level: item.level }
                            }}
                        </td>
                        <td *ngIf="shownFields.version">
                            {{ item.version }}
                        </td>
                        <td *ngIf="shownFields.ownerDepartment">
                            {{ item.ownerDepartment?.name }}
                        </td>
                        <td *ngIf="shownFields.lastEnhancementDate">
                            {{
                                (item.lastEnhancementDate
                                    | date : 'yyyy/MM/dd') !== '0001/01/01'
                                    ? (item.lastEnhancementDate
                                      | date : 'yyyy/MM/dd')
                                    : ' - '
                            }}
                        </td>
                        <ng-container *ngIf="shownFields.actions">
                            <td
                                *appHasAnyPermissionId="[
                                    permissionList.operationWrite,
                                    permissionList.operationDelete
                                ]"
                            >
                                <app-dropdown>
                                    <!-- Edit -->
                                    <a
                                        *ngIf="
                                            (permissionList.operationApproveUpdateRequest
                                                | hasPermissionId
                                                | async) ||
                                            ((permissionList.operationWrite
                                                | hasPermissionId
                                                | async) &&
                                                !item.hasPendingUpdateRequest)
                                        "
                                        [routerLink]="[
                                            '',
                                            'operation',
                                            'edit',
                                            item.id
                                        ]"
                                        class="btn btn-sm btn-info"
                                        [appTooltip]="
                                            'translate_edit' | translate
                                        "
                                    >
                                        <i class="fa-light fa-edit fa-fw"></i>
                                    </a>

                                    <!-- Delete -->
                                    <button
                                        *appHasPermissionId="
                                            permissionList.operationDelete
                                        "
                                        [disabled]="
                                            currentlyDeleting.includes(item.id)
                                        "
                                        (confirm)="delete(item)"
                                        [swal]="{
                                            title:
                                                'translate_delete_this_item_question_mark'
                                                | translate,
                                            confirmButtonText:
                                                'translate_yes' | translate,
                                            cancelButtonText:
                                                'translate_cancel' | translate,
                                            showCancelButton: true,
                                            showCloseButton: true
                                        }"
                                        class="btn btn-sm btn-danger"
                                        [appTooltip]="
                                            'translate_delete' | translate
                                        "
                                    >
                                        <i class="fas fa-trash fa-fw"></i>
                                    </button>
                                </app-dropdown>
                            </td>
                        </ng-container>
                    </tr>
                </ng-container>
            </tbody>
        </table>
    </div>

    <app-table-pagination
        [tableController]="tableController"
    ></app-table-pagination>
</app-list-loading>
