<app-alert
    *ngIf="!isEvaluatedAlready"
    label="translate_evaluation_alert_title"
    description="translate_evaluation_alert_description"
    mode="warning"
></app-alert>

<form class="mt-2 text-center" (ngSubmit)="submit()">
    <div class="table-responsive mb-2">
        <table>
            <thead>
                <tr>
                    <th>{{ 'translate_standard' | translate }}</th>
                    <th>{{ 'translate_total' | translate }}</th>
                    <th>{{ 'translate_result' | translate }}</th>
                </tr>
            </thead>
            <tbody>
                <ng-container
                    *ngFor="
                        let standard of partnershipContractPartnerEvaluations
                    "
                >
                    <tr>
                        <!-- Evaluation Label -->
                        <td>
                            {{ standard.label }}
                        </td>

                        <!-- Target -->
                        <td class="text-center">
                            {{ standard.target }}
                        </td>

                        <!-- Value -->
                        <td class="text-center">
                            <div>
                                <input
                                    type="number"
                                    dir="ltr"
                                    class="hide-arrows w-full text-center"
                                    [(ngModel)]="standard.value"
                                    [ngModelOptions]="{ standalone: true }"
                                    [max]="standard.target"
                                    [disabled]="
                                        isSubmitting ||
                                        isEvaluatedAlready ||
                                        data['isEvaluationLocked']
                                    "
                                />
                            </div>

                            <p
                                *ngIf="standard.value > standard.target"
                                class="text-center text-xs text-red-500"
                            >
                                {{
                                    'translate_evaluation_target_exceeded'
                                        | translate
                                }}
                            </p>
                        </td>
                    </tr>
                </ng-container>
            </tbody>
        </table>
    </div>

    <div *ngIf="totalTarget" class="my-5">
        {{ 'translate_total_target' | translate }}: {{ totalTarget }}
    </div>

    <button
        *ngIf="!isEvaluatedAlready && !data['isEvaluationLocked']"
        type="submit"
        class="btn-lg btn btn-primary"
        [disabled]="isSubmitting"
    >
        <app-loading-ring *ngIf="isSubmitting" class="me-2"></app-loading-ring>
        <i class="fas fa-save me-2"></i>
        <span>{{ 'translate_save' | translate }}</span>
    </button>
</form>
