import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '@masar/env/environment';
import { Observable } from 'rxjs';
import { PartnershipContractPartnerEvaluationDto } from '@masar/pages/partnership-contract/dto';
import { Result } from 'mnm-webapp';
import { map } from 'rxjs/operators';
import { getBodyAsUrlParams } from '@masar/features/http-crud/utils';

@Injectable()
export class PartnerEvaluationService {
    public constructor(private readonly http: HttpClient) {}

    public getRating(
        id: string
    ): Observable<PartnershipContractPartnerEvaluationDto[]> {
        const url = `${environment.apiUrl}/partnership/${id}/partner-evaluation`;
        return this.http
            .get<Result<PartnershipContractPartnerEvaluationDto[]>>(url)
            .pipe(map(result => result.extra));
    }

    public updateRating(
        id: string,
        rating: PartnershipContractPartnerEvaluationDto[]
    ): Observable<void> {
        const url = `${environment.apiUrl}/partnership/${id}/partner-evaluation`;
        return this.http
            .put<Result<void>>(url, getBodyAsUrlParams({ rating }, 'data'))
            .pipe(map(result => result.extra));
    }
}
