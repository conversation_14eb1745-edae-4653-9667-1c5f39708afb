import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    Input,
    NgModuleRef,
    TemplateRef,
    ViewChild,
} from '@angular/core';
import { ModalService, NotificationService } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { finalize, tap } from 'rxjs/operators';
import { TableResult } from '@masar/common/misc/table';
import { Kpi } from '@masar/common/models';
import { TranslateService } from '@ngx-translate/core';
import { KpiListFullComponent } from '../kpi-list-full/kpi-list-full.component';

@Component({
    selector: 'app-kpi-linker',
    templateUrl: './kpi-linker.component.html',
    styles: [':host {display: block}'],
})
export class KpiLinkerComponent implements AfterViewInit {
    @ViewChild('controlColumnTemplateRef')
    public controlColumnTemplateRef: TemplateRef<any>;

    @Input() public mode: 'default' | 'view' = 'default';
    @Input() public listLinkedKpisCallback: (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Kpi>>;
    @Input() public listUnlinkedKpisCallback?: (
        keyword: string,
        kpiNumber: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Kpi>>;
    @Input() public linkingCallback: (fileId: string) => Observable<string>;
    @Input() public unlinkingCallback: (fileId: string) => Observable<string>;

    @Input() public orderCallback: (
        orderedItems: Kpi[],
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<string>;

    @Input() public requiredPermissionForLinking: string;
    @Input() public requiredPermissionForUnlinking: string;

    @ViewChild(KpiListFullComponent) private list: KpiListFullComponent;
    @ViewChild('selectButtonTemplate')
    private selectButtonTemplate: TemplateRef<any>;

    public currentlyProcessing = new Set<string>();
    private unlinkedListComponentModal: KpiListFullComponent;

    public constructor(
        private notificationService: NotificationService,
        private modalService: ModalService,
        private moduleRef: NgModuleRef<any>,
        private readonly translateService: TranslateService,
        private changeDetectorRef: ChangeDetectorRef
    ) {}

    public ngAfterViewInit(): void {
        this.list.alternateListCallback = filter => {
            return this.listLinkedKpisCallback(
                filter.data.keyword,
                filter.pageNumber,
                filter.pageSize
            );
        };

        this.list.orderCallback = this.orderCallback
            ? (orderedItems, filter) => {
                  return this.orderCallback(
                      orderedItems,
                      filter.data.keyword,
                      filter.pageNumber,
                      filter.pageSize
                  ).pipe(
                      tap(message =>
                          this.notificationService.notifySuccess(message)
                      )
                  );
              }
            : null;

        if (this.mode === 'default') {
            this.list.list.customFields = [
                {
                    name: '',
                    template: this.controlColumnTemplateRef,
                },
            ];
        }

        // Changing input programmatically does not
        // trigger ng on changes, should manually
        // call it.
        this.list.ngOnChanges({
            alternateListCallback: {
                previousValue: undefined,
                currentValue: this.list.alternateListCallback,
            } as any,
        });
        this.changeDetectorRef.detectChanges();
    }

    public linkKpi(item: Kpi): void {
        this.currentlyProcessing.add(item.id);
        this.linkingCallback(item.id)
            .pipe(finalize(() => this.currentlyProcessing.delete(item.id)))
            .subscribe(message => {
                this.notificationService.notifySuccess(message);

                // Refresh the unlinked kpi list.
                this.unlinkedListComponentModal.refreshItems();

                // Refresh the linked kpi list.
                this.list.refreshItems();
            });
    }

    public unlinkKpi(kpiId: string): void {
        this.currentlyProcessing.add(kpiId);

        this.unlinkingCallback(kpiId)
            .pipe(finalize(() => this.currentlyProcessing.delete(kpiId)))
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.list.refreshItems();
            });
    }

    public async showKpiDialog(): Promise<void> {
        this.unlinkedListComponentModal = await this.modalService.show(
            KpiListFullComponent,
            {
                size: { width: '70%' },
                moduleRef: this.moduleRef,
                title: this.translateService.instant(
                    'translate_link_with_kpis'
                ),
                beforeInit: c => {
                    c.shownFilters = ['name', 'number'];
                    c.alternateListCallback = this.listUnlinkedKpisCallback
                        ? filter => {
                              return this.listUnlinkedKpisCallback(
                                  filter.data.keyword,
                                  filter.data.kpiNumber,
                                  filter.pageNumber,
                                  filter.pageSize
                              );
                          }
                        : null;
                },
                onDismiss: () => {
                    this.unlinkedListComponentModal = null;
                },
            }
        );

        this.unlinkedListComponentModal.list.shownFields = ['name', 'cycle'];
        this.unlinkedListComponentModal.list.customFields = [
            {
                name: '',
                template: this.selectButtonTemplate,
            },
        ];
    }
}
