<div *ngIf="items && items.length !== 0" class="table-responsive">
    <table>
        <thead>
            <tr class="text-center">
                <th rowspan="2" *ngIf="shownFields.includes('plot')"></th>
                <th rowspan="2" *ngIf="shownFields.includes('name')">
                    {{ 'translate_kpi_name' | translate }}
                </th>
                <th rowspan="2" *ngIf="shownFields.includes('weight')">
                    {{ 'translate_weight' | translate }}
                </th>
                <ng-container
                    *ngFor="
                        let i of items[0].results.length | enumerate;
                        let x = index
                    "
                >
                    <th colspan="2">
                        {{ items[0].results[0].year + i }}
                    </th>
                </ng-container>
                <th *ngFor="let col of customFields"></th>
            </tr>

            <tr class="text-center">
                <ng-container
                    *ngFor="let result of items[0].results; let x = index"
                >
                    <th appTooltip="{{ 'translate_target' | translate }}">T</th>
                    <th appTooltip="{{ 'translate_result' | translate }}">R</th>
                </ng-container>
                <th *ngFor="let col of customFields">
                    {{ col.name }}
                </th>
            </tr>
        </thead>

        <tbody
            cdkDropList
            [cdkDropListDisabled]="!enableDrag"
            (cdkDropListDropped)="order.emit($event)"
        >
            <tr
                *ngFor="let item of items; let idx = index"
                cdkDrag
                [ngClass]="{
                    'bg-gray-100': idx % 2 === 0
                }"
            >
                <td *ngIf="shownFields.includes('plot')">
                    <button
                        (click)="showKpiPlot(item)"
                        class="btn btn-sm btn-primary mx-auto block whitespace-nowrap"
                    >
                        <i class="fa fa-chart-gantt"></i>
                    </button>
                </td>

                <td *ngIf="shownFields.includes('name')">
                    <a
                        *appHasPermissionId="
                            permissionList.kpiRead;
                            else nameWithNoPermission
                        "
                        [routerLink]="['', 'kpi', 'detail', item.id]"
                        >{{ item.name }}</a
                    >

                    <ng-template #nameWithNoPermission>
                        {{ item.name }}
                    </ng-template>
                </td>

                <td *ngIf="shownFields.includes('weight')" class="text-center">
                    {{
                        item.weight !== null && item.weight !== undefined
                            ? item.weight + '%'
                            : '-'
                    }}
                </td>

                <ng-container *ngFor="let result of item.results">
                    <td class="text-center">
                        {{
                            (result.target
                                | formatKpiResult
                                    : result.units
                                    : result.unitsDescription) ?? '-'
                        }}
                    </td>
                    <td class="text-center">
                        {{
                            (result.result
                                | round : 2
                                | formatKpiResult
                                    : result.units
                                    : result.unitsDescription) ?? '-'
                        }}
                    </td>
                </ng-container>

                <td *ngFor="let col of customFields">
                    <ng-container
                        [ngTemplateOutlet]="col.template"
                        [ngTemplateOutletContext]="{ item }"
                    ></ng-container>
                </td>
            </tr>
        </tbody>
    </table>
</div>
