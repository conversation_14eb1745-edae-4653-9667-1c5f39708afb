<app-filter-result-box>
    <!-- Name -->
    <app-search-input
        class="col-span-full md:col-span-2"
        [(ngModel)]="tableController.filter.data.keyword"
        [tableController]="tableController"
    ></app-search-input>

    <!-- Number -->
    <app-search-input
        *ngIf="shownFilters.includes('number')"
        [placeholder]="'translate_search_by_kpi_number'"
        [(ngModel)]="tableController.filter.data.kpiNumber"
        [tableController]="tableController"
    ></app-search-input>

    <!-- Tags -->
    <ng-select
        *ngIf="shownFilters.includes('tag')"
        [items]="tags"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_kpi_categorization' | translate }}"
        [(ngModel)]="tableController.filter.data.tagIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Cycles -->
    <ng-select
        *ngIf="shownFilters.includes('cycle')"
        [items]="cycles"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_measuring_cycle' | translate }}"
        [(ngModel)]="tableController.filter.data.cycles"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Types -->
    <ng-select
        *ngIf="shownFilters.includes('type')"
        [items]="types"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_type_kpi' | translate }}"
        [(ngModel)]="tableController.filter.data.typeIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- State -->
    <ng-select
        *ngIf="shownFilters.includes('state')"
        [items]="kpiProgressStates"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        [placeholder]="'translate_kpi_state' | translate"
        [(ngModel)]="tableController.filter.data.progresses"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Standards -->
    <ng-select
        *ngIf="shownFilters.includes('standard')"
        [items]="standards"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_standards' | translate }}"
        [(ngModel)]="tableController.filter.data.standardIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Strategic goals -->
    <ng-select
        *ngIf="shownFilters.includes('strategic_goal')"
        [items]="goals"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_strategic_goals' | translate }}"
        [(ngModel)]="tableController.filter.data.goalIds"
        (change)="tableController.filter$.next(true)"
        groupBy="yearRange"
    >
    </ng-select>

    <!-- Has benchmark -->
    <ng-select
        *ngIf="shownFilters.includes('has_benchmark')"
        [items]="hasBenchmarksItems"
        bindValue="id"
        bindLabel="name"
        [clearable]="false"
        [searchable]="false"
        placeholder="{{ 'translate_with_benchmarks' | translate }}"
        [(ngModel)]="tableController.filter.data.hasBenchmarks"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Department -->
    <ng-select
        *ngIf="shownFilters.includes('department')"
        [items]="departments"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_departments' | translate }}"
        [(ngModel)]="tableController.filter.data.departmentIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!--Measuring Department-->
    <app-tree-select
        *ngIf="showMeasuringDepartment"
        [(ngModel)]="tableController.filter.data.measuringDepartmentIds"
        [childrenFetcher]="measuringDepartmentChildrenFetcher"
        [parentFetcher]="measuringDepartmentParentFetcher"
        placeholder="{{ 'translate_kpi_measuring_department' | translate }}"
        [isMultiple]="true"
        (ngModelChange)="onMeasurementDepartmentChange($event)"
    >
    </app-tree-select>
    <!-- Evaluation status -->
    <ng-select
        *ngIf="shownFilters.includes('evaluation_status')"
        [items]="evaluationStatuses"
        bindValue="id"
        bindLabel="name"
        [clearable]="false"
        [searchable]="false"
        placeholder="{{ 'translate_with_evaluations' | translate }}"
        [(ngModel)]="tableController.filter.data.evaluationStatus"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Evaluation score bands -->
    <ng-select
        *ngIf="shownFilters.includes('evaluation_score_band')"
        [items]="evaluationScoreBands"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_evaluation_score_bands' | translate }}"
        [(ngModel)]="tableController.filter.data.evaluationScoreBandIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>
    <div></div>
</app-filter-result-box>

<!-- Table -->
<app-list-loading [items]="tableController.items">
    <!-- Default list view -->
    <app-kpi-list
        #list
        *ngIf="view === 'default'"
        [items]="tableController.items"
        [shownFields]="shownFields"
        (order)="order($event)"
        [enableDrag]="!!orderCallback && !tableController.isLoading"
        [customFields]="customFields"
    >
    </app-kpi-list>

    <!-- Kpi with results view -->
    <app-kpi-list-with-result
        #list
        *ngIf="view === 'with_result'"
        [items]="tableController.items"
        (order)="order($event)"
        [enableDrag]="!!orderCallback && !tableController.isLoading"
    >
    </app-kpi-list-with-result>
    <app-table-pagination
        [tableController]="tableController"
    ></app-table-pagination>
</app-list-loading>
