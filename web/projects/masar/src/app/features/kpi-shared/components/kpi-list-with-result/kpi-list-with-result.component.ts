import {
    Component,
    EventEmitter,
    Input,
    Output,
    TemplateRef,
} from '@angular/core';
import { permissionList } from '@masar/common/constants';
import { Kpi } from '@masar/common/models';
import { KpiResultAchievedChartComponent } from '@masar/features/masar/components';
import { ModalService } from 'mnm-webapp';
import { CdkDragDrop } from '@angular/cdk/drag-drop';

export type Field = 'plot' | 'name' | 'weight';

@Component({
    selector: 'app-kpi-list-with-result',
    templateUrl: './kpi-list-with-result.component.html',
})
export class KpiListWithResultComponent {
    @Input() public items: Kpi[];
    @Input() public customFields: {
        name: string;
        template: TemplateRef<any>;
    }[];
    @Input() public enableDrag: boolean = false;

    @Input() public shownFields: Field[] = ['plot', 'name', 'weight'];

    @Output() public order = new EventEmitter<CdkDragDrop<Kpi>>();

    public permissionList = permissionList;

    public constructor(private modalService: ModalService) {}

    public async showKpiPlot(kpi: Kpi): Promise<any> {
        const component = await this.modalService.show(
            KpiResultAchievedChartComponent,
            {
                beforeInit: c => (c.checkIsOwning = false),
            }
        );
        component.kpi = kpi;
        component.ngOnChanges();
    }
}
