import { Validators } from '@angular/forms';
import { MnmFormField } from '@masar/shared/components';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },

    {
        name: 'entityName',
        label: 'translate_entity_name',
        type: 'text',
        validators: [Validators.required],
    },

    {
        name: 'year',
        label: 'translate_year',
        type: 'number',
        validators: [Validators.required],
    },

    {
        name: 'result',
        label: 'translate_result',
        type: 'number',
    },

    {
        name: 'competitivePosition',
        label: 'translate_competitive_position',
        type: 'number',
    },
    {
        name: 'libraryFile',
        label: 'translate_attachment',
    },

    {
        name: 'competitiveEffect',
        label: 'translate_competitive_effect',
        type: 'textarea',
    },
];
