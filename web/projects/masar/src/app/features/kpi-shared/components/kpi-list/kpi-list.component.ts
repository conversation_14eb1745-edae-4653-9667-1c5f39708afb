import {
    Component,
    EventEmitter,
    Input,
    NgModuleRef,
    Output,
    TemplateRef,
    ViewChild,
} from '@angular/core';
import { Kpi } from '@masar/common/models';
import { ModalService } from 'mnm-webapp';
import { permissionList } from '@masar/common/constants';
import { KpiBenchmarkListComponent } from '../kpi-benchmark-list/kpi-benchmark-list.component';
import { CdkDragDrop } from '@angular/cdk/drag-drop';

export type Field =
    | 'evaluation'
    | 'achieved'
    | 'name'
    | 'cycle'
    | 'last_modified'
    | 'last_evaluation';

@Component({
    selector: 'app-kpi-list',
    templateUrl: './kpi-list.component.html',
    styleUrls: ['./kpi-list.component.scss'],
})
export class KpiListComponent {
    @ViewChild('list') public list: KpiListComponent;

    @Input() public items: Kpi[];
    @Input() public enableDrag: boolean = false;

    @Input() public shownFields: Field[] = [
        'evaluation',
        'achieved',
        'name',
        'cycle',
        'last_modified',
    ];

    @Input() public customFields: {
        name: string;
        template: TemplateRef<any>;
    }[];

    @Output() public order = new EventEmitter<CdkDragDrop<Kpi>>();

    public permissionList = permissionList;

    public constructor(
        private readonly modalService: ModalService,
        private readonly moduleRef: NgModuleRef<any>
    ) {}

    public async showBenchmarkListModal(item: Kpi): Promise<void> {
        await this.modalService.show(KpiBenchmarkListComponent, {
            moduleRef: this.moduleRef,
            beforeInit: c => {
                c.kpiId = item.id;
                c.mode = 'view';
            },
        });
    }
}
