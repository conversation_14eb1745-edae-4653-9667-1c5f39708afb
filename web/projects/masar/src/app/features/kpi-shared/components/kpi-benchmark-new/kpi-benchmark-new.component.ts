import {
    AfterViewInit,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    NgModuleRef,
    OnInit,
    Output,
    ViewChild,
} from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ModalService, NotificationService } from 'mnm-webapp';
import { KpiBenchmark } from '@masar/common/models';
import { MnmFormState } from '@masar/shared/components';
import { finalize, first, takeUntil } from 'rxjs/operators';
import { KpiBenchmarkService } from '../../kpi-benchmark.service';
import { fields } from './fields';
import { Subject } from 'rxjs';

@Component({
    selector: 'app-kpi-benchmark-new',
    templateUrl: './kpi-benchmark-new.component.html',
})
export class KpiBenchmarkNewComponent implements OnInit, AfterViewInit {
    @Input() public kpiId: string;
    @Input() public benchmark: KpiBenchmark;

    @Output()
    public benchmarkCreated: EventEmitter<KpiBenchmark> =
        new EventEmitter<KpiBenchmark>();

    @Output()
    public benchmarkUpdated: EventEmitter<KpiBenchmark> =
        new EventEmitter<KpiBenchmark>();

    @ViewChild('libraryFileTemplateRef')
    private libraryFileTemplateRef: ElementRef;

    public mode: 'new' | 'edit' = 'new';

    public formState: MnmFormState;
    public isSubmitting = false;

    public constructor(
        private kpiBenchmarkService: KpiBenchmarkService,
        private notificationService: NotificationService,
        private translateService: TranslateService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);
    }

    public static async showAsDialog(
        benchmark: KpiBenchmark,
        kpiId: string,
        modalService: ModalService,
        translateService: TranslateService,
        moduleRef: NgModuleRef<any>,
        createdCallback: (benchmark: KpiBenchmark) => void,
        updatedCallback: (benchmark: KpiBenchmark) => void
    ): Promise<void> {
        translateService
            .get(
                benchmark
                    ? 'translate_edit_benchmark'
                    : 'translate_new_benchmark'
            )
            .pipe(first())
            .subscribe(async title => {
                const unsubscribeAll = new Subject();

                const component = await modalService.show(
                    KpiBenchmarkNewComponent,
                    {
                        onDismiss: () => {
                            unsubscribeAll.next();
                            unsubscribeAll.complete();
                        },
                        beforeInit: c => {
                            c.kpiId = kpiId;
                            c.benchmark = benchmark;
                        },
                        moduleRef: moduleRef,
                        title,
                    }
                );

                // Monitor creation of new benchmarks.
                component.benchmarkCreated
                    .pipe(takeUntil(unsubscribeAll))
                    .subscribe(item => {
                        createdCallback(item);
                        modalService.dismiss(component);
                    });

                // Monitor updates of benchmarks.
                component.benchmarkUpdated
                    .pipe(takeUntil(unsubscribeAll))
                    .subscribe(item => {
                        updatedCallback(item);
                        modalService.dismiss(component);
                    });
            });
    }

    public ngOnInit(): void {
        this.mode = this.benchmark ? 'edit' : 'new';

        if (this.mode === 'edit') {
            this.fillForm();
        }
    }

    public ngAfterViewInit(): void {
        this.formState.get('libraryFile').customInputField =
            this.libraryFileTemplateRef;
    }

    public submit(): boolean | Promise<boolean> {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return false;
        }

        this.isSubmitting = true;

        const observable =
            this.mode === 'new'
                ? this.kpiBenchmarkService.create(
                      this.kpiId,
                      this.formState.group.getRawValue()
                  )
                : this.kpiBenchmarkService.update(
                      this.formState.group.getRawValue()
                  );

        return observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .toPromise()
            .then(item => {
                this.translateService
                    .get('translate_item_added_successfully')
                    .subscribe(str => {
                        this.notificationService.notifySuccess(str);
                    });

                if (this.mode === 'new') {
                    this.benchmarkCreated.emit(item);
                } else if (this.mode === 'edit') {
                    this.benchmarkUpdated.emit(item);
                }
                return true;
            })
            .catch(() => {
                return false;
            });
    }

    private fillForm(): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(this.benchmark[key]);
        }
    }
}
