<app-content contentTitle="{{ 'translate_evaluations_list' | translate }}">
    <app-list-loading content [items]="records">
        <div class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th>{{ 'translate_name' | translate }}</th>
                        <th>{{ 'translate_year' | translate }}</th>
                        <th>{{ 'translate_period' | translate }}</th>
                        <th>{{ 'translate_department' | translate }}</th>
                        <th>{{ 'translate_date' | translate }}</th>
                        <th>{{ 'translate_score' | translate }}</th>
                        <th class="w-0"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records">
                        <td class="text-center">{{ record.name }}</td>
                        <td class="text-center">
                            {{ record.year }}
                        </td>
                        <td class="text-center">
                            {{
                                record.period
                                    | measurementCycle : record.measurementCycle
                            }}
                        </td>
                        <td class="text-center">
                            {{ record.department?.name }}
                        </td>
                        <td class="text-center">
                            {{
                                record.creationTime
                                    | date : 'yyyy-MM-dd hh:mm a'
                            }}
                        </td>
                        <td class="text-center">
                            %{{ record.score * 100 | round : 2 }}
                        </td>
                        <td>
                            <button
                                class="btn btn-sm btn-info"
                                [appTooltip]="'translate_details' | translate"
                                (click)="showDetailDialog(record)"
                            >
                                <i class="fa fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </app-list-loading>
</app-content>
