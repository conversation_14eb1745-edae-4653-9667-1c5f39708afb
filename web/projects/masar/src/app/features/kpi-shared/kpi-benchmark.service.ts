import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { miscFunctions, Result } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@masar/env/environment';
import { KpiBenchmark } from '@masar/common/models';

@Injectable()
export class KpiBenchmarkService {
    public constructor(private httpClient: HttpClient) {}

    public list(kpiId: string, year?: number): Observable<KpiBenchmark[]> {
        let params = new HttpParams();
        if (year) {
            params = params.append('year', `${year}`);
        }

        return this.httpClient
            .get<Result<KpiBenchmark[]>>(
                `${environment.apiUrl}/kpi/benchmark/${kpiId}`,
                { params }
            )
            .pipe(map(res => res.extra));
    }

    public getBenchmarkYears(kpiId: string): Observable<number[]> {
        return this.httpClient
            .get<Result<number[]>>(
                `${environment.apiUrl}/kpi/benchmark-year/${kpiId}`
            )
            .pipe(map(res => res.extra));
    }

    public create(
        kpiId: string,
        benchmark: KpiBenchmark
    ): Observable<KpiBenchmark> {
        return this.httpClient
            .post<Result<KpiBenchmark>>(
                `${environment.apiUrl}/kpi/benchmark/${kpiId}`,
                miscFunctions.objectToURLParams({
                    benchmark: JSON.stringify(benchmark),
                })
            )
            .pipe(map(res => res.extra));
    }

    public update(benchmark: KpiBenchmark): Observable<KpiBenchmark> {
        return this.httpClient
            .put<Result<KpiBenchmark>>(
                `${environment.apiUrl}/kpi/benchmark`,
                miscFunctions.objectToURLParams({
                    benchmark: JSON.stringify(benchmark),
                })
            )
            .pipe(map(res => res.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result>(`${environment.apiUrl}/kpi/benchmark/${id}`)
            .pipe(map(res => res.messages[0]));
    }
}
