import { Component } from '@angular/core';
import { AppSettingFetcherService } from '@masar/core/services';
import { TranslationService } from '@ng-omar/translation';
import { appVersion } from '@masar/common/constants';
import { NavigationEnd, NavigationStart, Router } from '@angular/router';
import { animations } from './animations';

@Component({
    templateUrl: './auth-layout.component.html',
    animations: [animations],
})
export class AuthLayoutComponent {
    public readonly versionNumber = appVersion;
    public loginBackgroundBlobUrl: string;
    public animationState: 'enter' | 'leave';

    public constructor(
        public appSettingFetcherService: AppSettingFetcherService,
        public translationService: TranslationService,
        public router: Router
    ) {
        appSettingFetcherService.appLoginBackground().subscribe(blob => {
            this.loginBackgroundBlobUrl = URL.createObjectURL(blob);
        });

        router.events.subscribe(event => {
            if (event instanceof NavigationStart) {
                setTimeout(() => (this.animationState = 'leave'));
            } else if (event instanceof NavigationEnd) {
                setTimeout(() => (this.animationState = 'enter'));
            }
        });
    }
}
