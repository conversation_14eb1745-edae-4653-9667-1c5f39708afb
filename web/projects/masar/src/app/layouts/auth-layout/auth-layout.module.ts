import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AuthLayoutRouting } from './auth-layout.routing';
import { AuthLayoutComponent } from './auth-layout.component';
import { EasterModule } from '@masar/features/easter/easter.module';
import { SharedModule } from '@masar/shared/shared.module';
import { TranslationModule } from '@ng-omar/translation';

@NgModule({
    declarations: [AuthLayoutComponent],
    imports: [
        CommonModule,
        AuthLayoutRouting,
        SharedModule,
        EasterModule,
        TranslationModule,
    ],
})
export class AuthLayoutModule {}
