import { permissionList } from '@masar/common/constants';
import { NavItem } from '@masar/shared/components/sidebar/types';
import { isNotOrigin, isOrigin } from '@masar/common/utils';
import { OrganizationOrigin } from '@masar/common/enums';

export const navItems: NavItem[] = [
    // Excellence dashboard
    {
        title: 'translate_excellence_dashboard',
        type: 'item',
        icon: 'fa-light fa-trophy',
        link: ['', 'excellence'],
    },

    // Files library
    {
        title: 'translate_files_library',
        type: 'item',
        icon: 'fa-light fa-book',
        link: ['', 'library'],
        isHidden: isOrigin([
            OrganizationOrigin.injaz,
            OrganizationOrigin.police,
        ]),
    },

    // Users requests
    {
        title: 'translate_users_requests',
        type: 'item',
        icon: 'fa-light fa-sticky-note',
        link: ['', 'user-request'],
    },

    // Ajman Excel Sheet
    {
        title: 'translate_carrying_capacity_system',
        type: 'item',
        icon: 'fa-light fa-chart-line-up',
        link: ['', 'excel-sheet'],
        isHidden: isNotOrigin([
            OrganizationOrigin.police,
            OrganizationOrigin.localhost,
        ]),
    },

    // Department Section
    {
        title: 'translate_departments_stats',
        type: 'item',
        icon: 'fa-light fa-chart-line-up',
        link: ['', 'dashboard', 'department'],
    },

    // Performance management
    {
        title: 'translate_performance_management',
        type: 'section',
        permissionIdList: [
            permissionList.kpiRead,
            permissionList.operationRead,
        ],
        children: [
            // Kpis
            {
                title: 'translate_kpis',
                type: 'expandable',
                icon: 'fa-light fa-chart-bar',
                permissionIdList: [permissionList.kpiRead],
                children: [
                    // Kpis / new
                    {
                        title: 'translate_new_kpi',
                        type: 'item',
                        link: ['', 'kpi', 'new'],
                        permissionIdList: [permissionList.kpiWrite],
                    },

                    // Kpis / manage
                    {
                        title: 'translate_manage_kpis',
                        type: 'item',
                        link: ['', 'kpi'],
                    },

                    // Kpis / hidden
                    {
                        title: 'translate_hidden_kpis',
                        type: 'item',
                        link: ['', 'kpi', 'hidden'],
                        permissionIdList: [permissionList.fullAccess],
                    },

                    // Kpis / archived
                    {
                        title: 'translate_retired_kpis_basket',
                        type: 'item',
                        link: ['', 'kpi', 'archived'],
                    },

                    // Kpis / removed
                    {
                        title: 'translate_deleted_kpis_basket',
                        type: 'item',
                        link: ['', 'kpi', 'removed'],
                        permissionIdList: [permissionList.kpiDelete],
                    },

                    // Kpis / data entry request
                    {
                        title: 'translate_data_entry_requests',
                        type: 'item',
                        link: ['', 'kpi', 'data-entry-request'],
                        permissionIdList: [
                            permissionList.kpiResultEntryRequest,
                            permissionList.kpiResultEntryRequestWrite,
                            permissionList.kpiResultEntryRequestDelete,
                        ],
                    },

                    // Kpis / data entry response
                    {
                        title: 'translate_data_entry_responses',
                        type: 'item',
                        link: ['', 'kpi', 'data-entry-response'],
                        permissionIdList: [permissionList.kpiRead],
                    },

                    // Kpis / data entry response
                    {
                        title: 'translate_dynamic_data_entry_requests',
                        type: 'item',
                        link: ['', 'kpi', 'dynamic-data-entry-request'],
                        permissionIdList: [
                            permissionList.kpiResultDynamicApprove,
                        ],
                    },
                ],
            },

            // Kpi Evaluations
            {
                title: 'translate_kpi_evaluation',
                type: 'expandable',
                icon: 'fa-light fa-chart-bar',
                permissionIdList: [
                    permissionList.kpiResultPeriodExportEvaluation,
                ],
                isHidden: isNotOrigin([
                    OrganizationOrigin.police,
                    OrganizationOrigin.staging,
                    OrganizationOrigin.localhost,
                ]),
                children: [
                    // Statistics
                    {
                        title: 'translate_kpi_evaluation_statistics',
                        type: 'item',
                        link: ['', 'kpi-evaluation', 'dashboard'],
                    },

                    // Kpi Evaluations report
                    {
                        title: 'translate_kpi_evaluation_report',
                        type: 'item',
                        link: ['', 'kpi-evaluation', 'report'],
                    },

                    // Kpi Evaluations
                    {
                        title: 'translate_kpi_evaluation_list',
                        type: 'item',
                        link: ['', 'kpi-evaluation'],
                    },
                ],
            },

            // Operations
            {
                title: 'translate_operations',
                type: 'expandable',
                icon: 'fa-light fa-cogs',
                permissionIdList: [permissionList.operationRead],
                children: [
                    // Operations / Statistics
                    {
                        title: 'translate_operations_statistics',
                        type: 'item',
                        link: ['', 'operation', 'dashboard'],
                        permissionIdList: [permissionList.operationRead],
                    },

                    // Operations / new
                    {
                        title: 'translate_new_operation',
                        type: 'item',
                        link: ['', 'operation', 'new'],
                        permissionIdList: [permissionList.operationWrite],
                    },

                    // Operations / manage
                    {
                        title: 'translate_operations',
                        type: 'item',
                        link: ['', 'operation'],
                    },

                    // Operations / tree
                    {
                        title: 'translate_main_operations',
                        type: 'item',
                        link: ['', 'operation', 'tree'],
                    },

                    // Operations / update requests
                    {
                        title: 'translate_update_requests',
                        type: 'item',
                        link: ['', 'operation', 'update-request'],
                        permissionIdList: [
                            permissionList.operationApproveUpdateRequest,
                        ],
                    },
                ],
            },

            // Operational plans
            {
                title: 'translate_operational_plans',
                type: 'expandable',
                icon: 'fa-light fa-ruler',
                permissionIdList: [permissionList.planRead],
                children: [
                    // Operational plans / Statistics
                    {
                        title: 'translate_operation_plan_statistics',
                        type: 'item',
                        link: ['', 'plan', 'dashboard'],
                        permissionIdList: [permissionList.planRead],
                    },

                    // Operational plans / new
                    {
                        title: 'translate_new_operational_plan',
                        type: 'item',
                        link: ['', 'plan', 'new'],
                        permissionIdList: [permissionList.planWrite],
                    },

                    // Operational plans / manage
                    {
                        title: 'translate_manage_operational_plans',
                        type: 'item',
                        link: ['', 'plan'],
                    },

                    // Operational plans / not approved
                    {
                        title: 'translate_not_approved_plans',
                        type: 'item',
                        link: ['', 'plan', 'not-approved'],
                    },

                    // Operational plans / plan tasks
                    {
                        title: 'translate_manage_tasks',
                        type: 'item',
                        link: ['', 'plan-task'],
                    },

                    // Operational plans / plan subtasks
                    {
                        title: 'translate_manage_procedures',
                        type: 'item',
                        link: ['', 'plan-subtask'],
                    },

                    // Operational plans / plan subtasks
                    {
                        title: 'translate_finalizable_subsubtasks',
                        type: 'item',
                        link: [
                            '',
                            'plan-subtask',
                            'approvable-finalizable-subsubtask',
                        ],
                        permissionIdList: [
                            permissionList.planSubsubtaskFinalApproval,
                        ],
                    },
                ],
            },

            // Benchmarks
            {
                title: 'translate_benchmarks',
                type: 'expandable',
                icon: 'fa-light fa-compress-alt',
                permissionIdList: [permissionList.benchmarkRead],
                children: [
                    // Benchmarks / Statistics
                    {
                        title: 'translate_benchmarks_statistics',
                        type: 'item',
                        link: ['', 'benchmark', 'dashboard'],
                    },

                    // Benchmarks / new
                    {
                        title: 'translate_new_benchmark',
                        type: 'item',
                        link: ['', 'benchmark', 'new'],
                        permissionIdList: [permissionList.benchmarkWrite],
                    },

                    // Benchmarks / manage
                    {
                        title: 'translate_manage_benchmarks',
                        type: 'item',
                        link: ['', 'benchmark'],
                    },
                ],
            },
        ],
    },

    // partners
    {
        title: 'translate_partners',
        type: 'expandable',
        icon: 'fa-light fa-handshake',
        permissionIdList: [
            permissionList.partner,
            permissionList.partnershipRead,
        ],
        children: [
            // Partners / Statistics
            {
                title: 'translate_partners_statistics',
                type: 'item',
                link: ['', 'partner', 'dashboard'],
                permissionIdList: [permissionList.partner],
            },

            // partners / new
            {
                title: 'translate_add_new_partner',
                type: 'item',
                link: ['', 'partner', 'new'],
                permissionIdList: [permissionList.partner],
            },

            // partners / manage
            {
                title: 'translate_manage_partners',
                type: 'item',
                link: ['', 'partner'],
                permissionIdList: [permissionList.partner],
            },

            //new partnerships contracts
            {
                title: 'translate_add_new_partnership_contract',
                type: 'item',
                link: ['', 'partnership-contract', 'new'],
                permissionIdList: [permissionList.partnership],
            },

            // partnerships contracts
            {
                title: 'translate_manage_partnership_contracts',
                type: 'item',
                link: ['', 'partnership-contract'],
                permissionIdList: [permissionList.partnershipRead],
            },

            // partnership termination requests
            {
                title: 'translate_partnership_termination_requests',
                type: 'item',
                link: ['', 'partnership-termination-request'],
                permissionIdList: [permissionList.partnershipRead],
            },
        ],
    },

    // services
    {
        title: 'translate_services',
        type: 'expandable',
        icon: 'fa-light fa-bell-concierge',
        permissionIdList: [permissionList.service],
        children: [
            // Services / Statistics
            {
                title: 'translate_services_statistics',
                type: 'item',
                link: ['', 'service', 'dashboard'],
            },

            // Operational services / new
            {
                title: 'translate_new_service',
                type: 'item',
                link: ['', 'service', 'new'],
            },

            // Operational services / manage
            {
                title: 'translate_manage_services',
                type: 'item',
                link: ['', 'service'],
            },
        ],
    },

    // Risk management
    {
        title: 'translate_risk',
        type: 'expandable',
        icon: 'fa-light fa-bolt',
        permissionIdList: [permissionList.riskRead],
        isHidden: isOrigin([
            OrganizationOrigin.injaz,
            OrganizationOrigin.police,
        ]),
        children: [
            // Risks / dashboard
            {
                title: 'translate_risk_statistics',
                type: 'item',
                link: ['', 'risk', 'dashboard'],
            },

            // Risks / dashboard / department
            {
                title: 'translate_departments_risk_statistics',
                type: 'item',
                link: ['', 'risk', 'dashboard', 'department'],
            },

            // Risks / dashboard / evaluation
            {
                title: 'translate_risk_evaluation_statistics',
                type: 'item',
                link: ['', 'risk', 'dashboard', 'evaluation'],
            },

            // Risks / new
            {
                title: 'translate_new_risk',
                type: 'item',
                permissionIdList: [permissionList.riskWrite],
                link: ['', 'risk', 'new'],
            },

            // Risks / manage
            {
                title: 'translate_risk_management',
                type: 'item',
                link: ['', 'risk'],
            },
        ],
    },

    // Excellence management
    {
        title: 'translate_excellence_management',
        type: 'section',
        tag: 'excellence_management',
        children: [
            // Tournaments
            {
                title: 'translate_excellence_tournaments',
                type: 'expandable',
                icon: 'fa-light fa-chess',
                tag: 'excellence_tournaments',
                children: [
                    {
                        title: 'translate_new_tournament',
                        type: 'item',
                        link: ['', 'tournament', 'new'],
                        permissionIdList: [permissionList.tournament],
                    },
                    {
                        title: 'translate_manage_tournaments',
                        type: 'item',
                        link: ['', 'tournament'],
                        permissionIdList: [permissionList.tournamentRead],
                    },
                    {
                        title: 'translate_manage_teams',
                        type: 'item',
                        link: ['', 'tournament', 'standard', 'team'],
                        permissionIdList: [permissionList.tournamentRead],
                    },
                    {
                        title: 'translate_main_tasks',
                        type: 'item',
                        link: ['', 'tournament', 'main-task'],
                    },
                    {
                        title: 'translate_subtasks',
                        type: 'item',
                        link: ['', 'tournament', 'subtask'],
                    },
                    {
                        title: 'translate_my_tasks',
                        type: 'item',
                        tag: 'my_tasks',
                        link: ['', 'tournament', 'my-task'],
                    },
                ],
            },

            // Capabilities
            {
                title: 'translate_capabilities',
                type: 'expandable',
                icon: 'fa-light fa-wrench',
                permissionIdList: [permissionList.capability],
                children: [
                    // Capabilities / Statistics
                    {
                        title: 'translate_capabilities_statistics',
                        type: 'item',
                        link: ['', 'capability', 'dashboard'],
                    },

                    // Capabilities / new
                    {
                        title: 'translate_new_capability',
                        type: 'item',
                        link: ['', 'capability', 'new'],
                    },

                    // Capabilities / manage
                    {
                        title: 'translate_manage_capabilities',
                        type: 'item',
                        link: ['', 'capability'],
                    },
                ],
            },

            // opportunities
            {
                title: 'translate_opportunities',
                type: 'expandable',
                icon: 'fa-light fa-chart-line-up',
                permissionIdList: [permissionList.improvementOpportunityRead],
                children: [
                    // Operational opportunities / Statistics
                    {
                        title: 'translate_opportunities_statistics',
                        type: 'item',
                        link: ['', 'opportunity', 'dashboard'],
                        permissionIdList: [
                            permissionList.improvementOpportunityRead,
                        ],
                    },

                    // Operational opportunities / new
                    {
                        title: 'translate_new_opportunity',
                        type: 'item',
                        link: ['', 'opportunity', 'new'],
                        permissionIdList: [
                            permissionList.improvementOpportunityWrite,
                        ],
                    },

                    // Operational opportunities / manage
                    {
                        title: 'translate_manage_opportunities',
                        type: 'item',
                        link: ['', 'opportunity'],
                    },
                ],
            },
            // Innovation & Innovator
            {
                title: 'translate_innovations',
                type: 'expandable',
                icon: 'fa-light fa-lightbulb',
                permissionIdList: [
                    permissionList.innovator,
                    permissionList.innovation,
                ],
                children: [
                    // Innovator / profile
                    {
                        title: 'translate_my_profile',
                        type: 'item',
                        link: ['', 'innovator', 'profile'],
                    },

                    // Innovator / new
                    {
                        title: 'translate_new_innovator',
                        type: 'item',
                        link: ['', 'innovator', 'new'],
                        permissionIdList: [permissionList.innovation],
                    },

                    // Innovation / new
                    {
                        title: 'translate_new_innovation',
                        type: 'item',
                        link: ['', 'innovation', 'new'],
                    },

                    // Innovator / manage
                    {
                        title: 'translate_manage_innovators',
                        type: 'item',
                        link: ['', 'innovator'],
                        permissionIdList: [permissionList.innovation],
                    },

                    // Innovation / manage
                    {
                        title: 'translate_manage_innovations',
                        type: 'item',
                        link: ['', 'innovation'],
                        permissionIdList: [permissionList.innovation],
                    },

                    // Activities
                    {
                        title: 'translate_activities',
                        type: 'item',
                        link: ['', 'activity'],
                        permissionIdList: [permissionList.innovation],
                    },

                    // Activities / new
                    {
                        title: 'translate_new_activity',
                        type: 'item',
                        link: ['', 'activity', 'new'],
                        permissionIdList: [permissionList.innovation],
                    },
                ],
            },
        ],
    },

    // Reports
    {
        title: 'translate_reports',
        type: 'section',
        icon: 'fa-light fa-filter',
        permissionIdList: [
            permissionList.reportKpi,
            permissionList.reportDepartment,
        ],
        children: [
            // Reports / kpis
            {
                title: 'translate_kpi_reports',
                type: 'item',
                icon: 'fa-light fa-chart-line-up',
                link: ['', 'report', 'kpi'],
                permissionIdList: [permissionList.reportKpi],
            },

            // Reports / kpi results
            {
                title: 'translate_kpi_results_reports',
                type: 'item',
                link: ['', 'report', 'kpi-result', 'result'],
                permissionIdList: [permissionList.reportKpi],
            },

            // Reports / departments
            {
                title: 'translate_department_reports',
                type: 'item',
                icon: 'fa-light fa-chart-line-up',
                link: ['', 'report', 'department', 'result'],
                permissionIdList: [permissionList.reportDepartment],
            },

            // Statistical reports
            {
                title: 'translate_statistical_reports',
                type: 'expandable',
                icon: 'fa-light fa-chart-line-up',
                permissionIdList: [permissionList.statisticalReportRead],
                children: [
                    // Benchmarks / Statistics
                    {
                        title: 'translate_statistical_reports_statistics',
                        type: 'item',
                        link: ['', 'statistical-report', 'dashboard'],
                        permissionIdList: [
                            permissionList.statisticalReportRead,
                        ],
                    },
                    {
                        title: 'translate_new_statistical_report',
                        type: 'item',
                        icon: 'fa-light fa-chart-line-up',
                        link: ['', 'statistical-report', 'new'],
                        permissionIdList: [
                            permissionList.statisticalReportWrite,
                        ],
                    },
                    {
                        title: 'translate_manage_statistical_reports',
                        type: 'item',
                        icon: 'fa-light fa-chart-line-up',
                        link: ['', 'statistical-report'],
                        permissionIdList: [
                            permissionList.statisticalReportRead,
                        ],
                    },
                ],
            },
        ],
    },

    // Control
    {
        title: 'translate_control',
        type: 'section',
        permissionIdList: [
            permissionList.userRead,
            permissionList.departmentRead,
        ],
        children: [
            // Controls / notifications
            {
                title: 'translate_notifications',
                type: 'item',
                icon: 'fa-light fa-bell',
                permissionIdList: [permissionList.notification],
                link: ['', 'notification'],
            },
        ],
    },
];
