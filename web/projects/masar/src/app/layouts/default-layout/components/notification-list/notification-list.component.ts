import { Component, Input } from '@angular/core';
import { Notification } from '@masar/common/models';
import { MyNotificationService } from '@masar/pages/my-notification/my-notification.service';
import { finalize } from 'rxjs/operators';

@Component({
    selector: 'app-notification-list',
    templateUrl: './notification-list.component.html',
    providers: [MyNotificationService],
})
export class NotificationListComponent {
    @Input() public notificationBadgeCount: number = 0;

    public notifications: Notification[] = [];

    public isLoading = false;

    public showNotificationList = false;
    public listScrollPosition: number = 0;

    public readonly now = new Date();

    public constructor(
        // FIXME: Move this service to shared service
        private readonly myNotificationService: MyNotificationService
    ) {}

    public markAllAsRead(): void {
        this.myNotificationService.markAllAsRead().subscribe();
    }

    public navigateTo(notification: Notification): void {
        this.showNotificationList = false;
        this.myNotificationService.navigateTo(notification);
    }

    public showNotifications(): void {
        if (this.showNotificationList === true) return;

        this.showNotificationList = true;

        this.isLoading = true;

        this.myNotificationService
            .list('', null, null, null, 0, 20, true)
            .pipe(finalize(() => (this.isLoading = false)))
            .subscribe(
                notifications => (this.notifications = notifications.items)
            );
    }
}
