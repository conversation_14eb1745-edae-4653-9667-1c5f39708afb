<div class="sidebar-user flex flex-row items-center justify-between gap-1 p-4">
    <div class="flex">
        <a
            [routerLink]="['', 'profile']"
            class="me-2 h-10 w-10 overflow-hidden rounded-full border-2 border-primary bg-cover"
        >
            <app-image
                #avatar
                [imageObservable]="imageApiService.user()"
                [isObjectFitContain]="false"
                style="height: 100%"
            ></app-image>
        </a>
        <p class="flex flex-col justify-center text-sm font-bold">
            {{ userFullName }}
        </p>
    </div>
    <button
        class="rounded-full px-2 py-1 transition-all hover:bg-primary-700 hover:text-white"
        [appTooltip]="'translate_logout' | translate"
        [swal]="{
            title: 'translate_logout_question_mark' | translate,
            text: 'translate_do_you_want_to_logout_question_mark' | translate,
            confirmButtonText: 'translate_yes' | translate,
            cancelButtonText: 'translate_cancel' | translate,
            showCancelButton: true,
            showCloseButton: true
        }"
        (confirm)="logout()"
    >
        <em class="fa-solid fa-right-from-bracket"></em>
    </button>
</div>
