<ng-container *ngIf="!isSidebarCollapsed">
    <app-image
        #appLogo
        *ngIf="appLogo$"
        [imageObservable]="appLogo$"
        style="height: 75px; width: 150px"
    ></app-image>

    <div
        *ngIf="
            !appLogo$ && appSettingFetcherService.snapshot?.appName as appName
        "
        class="flex h-full w-full items-center justify-center text-center"
    >
        <h2 class="text-2xl font-bold">
            {{ appName }}
        </h2>
    </div>
</ng-container>
