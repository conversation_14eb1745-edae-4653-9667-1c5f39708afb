import {
    AfterViewInit,
    Component,
    OnDestroy,
    ViewChildren,
} from '@angular/core';
import { ImageApiService } from '@masar/core/services';
import {
    BroadcasterService,
    NotificationService,
    OauthService,
} from 'mnm-webapp';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { ImageComponent } from '@masar/shared/components';

@Component({
    selector: 'app-user-card',
    templateUrl: 'user-card.component.html',
})
export class UserCardComponent implements AfterViewInit, OnDestroy {
    @ViewChildren('avatar') private avatars: ImageComponent[];

    public userFullName: string = '';

    private readonly unsubscribeAll = new Subject();

    public constructor(
        public readonly imageApiService: ImageApiService,
        private readonly oauthService: OauthService,
        private readonly translateService: TranslateService,
        private readonly router: Router,
        private readonly notificationService: NotificationService,
        private readonly broadcasterService: BroadcasterService
    ) {
        this.monitorUserInfoChanges();
    }

    public ngAfterViewInit(): void {
        this.broadcasterService
            .on('image-changed')
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(() => this.avatars.forEach(x => x.refreshImage()));
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public logout(): void {
        this.translateService
            .get('translate_logging_out_successful')
            .subscribe(str => {
                this.oauthService.logout();
                // noinspection JSIgnoredPromiseFromCall
                this.router.navigate(['', 'auth', 'login']).then();
                this.notificationService.notifySuccess(str);
            });
    }

    private monitorUserInfoChanges(): void {
        this.oauthService.userInfo$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(userInfo => {
                if (userInfo.isLoggedIn) {
                    this.userFullName = userInfo.claims.find(
                        x =>
                            x.type ===
                            'full_name_' + this.translateService.currentLang
                    )?.value;
                } else {
                    this.userFullName = '';
                }
            });
    }
}
