import {
    Component,
    EventEmitter,
    Input,
    NgModuleRef,
    OnDestroy,
    Output,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ModalService } from 'mnm-webapp';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { permissionList } from '@masar/common/constants';
import {
    ImageApiService,
    MiscApiService,
    NotificationUnreadCountService,
    YearService,
} from '@masar/core/services';
import { DropdownAppsComponent } from '../dropdown-apps/dropdown-apps.component';
import { TranslationService } from '@ng-omar/translation';
import { OrganizationOrigin } from '@masar/common/enums';
import { DefaultLayoutService } from '@masar/layouts/default-layout/services/default-layout.service';

@Component({
    selector: 'app-header',
    templateUrl: './header.component.html',
})
export class HeaderComponent implements OnDestroy {
    @Input() public isSidebarCollapsed: boolean;
    @Output() public toggleSidebarCollapse = new EventEmitter();

    public isFullScreen: boolean = this.checkFullScreen();

    public years: number[];

    public notificationBadgeCount = 0;
    public newUserRequestCount = 0;

    public dropdownVisible = false;

    public readonly permissionList = permissionList;

    public readonly organizationOrigin = OrganizationOrigin;

    private unsubscribeAll = new Subject();

    public constructor(
        public readonly yearService: YearService,
        public readonly imageApiService: ImageApiService,
        public readonly translationService: TranslationService,
        public readonly defaultLayoutService: DefaultLayoutService,
        private readonly modalService: ModalService,
        private readonly moduleRef: NgModuleRef<any>,
        private readonly translateService: TranslateService,
        miscApiService: MiscApiService,
        unreadCountService: NotificationUnreadCountService
    ) {
        miscApiService.years().subscribe(items => (this.years = items));

        // Monitor my notifications counter.
        unreadCountService.count$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(count => (this.notificationBadgeCount = count));

        // Subscribe to new user requests
        unreadCountService.newUserRequestUnread$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(count => (this.newUserRequestCount = count));
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public changeYear(change: number): void {
        const year = this.yearService.get();
        let newYear = this.years.includes(year + change) ? year + change : year;
        this.yearService.set(newYear);
    }

    public onSwitchFullScreen(): void {
        if (this.checkFullScreen()) this.exitFullScreen();
        else this.setFullScreen();
    }

    public async showAppsDialog(): Promise<void> {
        const subject = new Subject();

        const component = await this.modalService.show(DropdownAppsComponent, {
            title: this.translateService.instant('translate_apps'),
            moduleRef: this.moduleRef,
            onDismiss: () => {
                subject.next();
                subject.complete();
            },
        });

        component.done.pipe(takeUntil(subject)).subscribe(_ => {
            this.modalService.dismiss(component);
        });
    }

    private checkFullScreen(): boolean {
        const fsDoc: any = document;

        return !!(
            fsDoc.fullscreenElement ||
            fsDoc.mozFullScreenElement ||
            fsDoc.webkitFullscreenElement ||
            fsDoc.msFullscreenElement
        );
    }

    private setFullScreen(): void {
        this.isFullScreen = true;
        const elem: any = document.documentElement;
        if (elem.requestFullscreen) {
            elem.requestFullscreen();
        } else if (elem?.mozRequestFullScreen) {
            elem.mozRequestFullScreen();
        } else if (elem.webkitRequestFullscreen) {
            elem.webkitRequestFullscreen();
        } else if (elem.msRequestFullscreen) {
            elem.msRequestFullscreen();
        }
    }

    private exitFullScreen(): void {
        this.isFullScreen = false;
        const fsDoc: any = document;
        if (fsDoc.exitFullscreen) fsDoc.exitFullscreen();
        else if (fsDoc.msExitFullscreen) fsDoc.msExitFullscreen();
        else if (fsDoc.mozCancelFullScreen) fsDoc.mozCancelFullScreen();
        else if (fsDoc.webkitExitFullscreen) fsDoc.webkitExitFullscreen();
    }
}
