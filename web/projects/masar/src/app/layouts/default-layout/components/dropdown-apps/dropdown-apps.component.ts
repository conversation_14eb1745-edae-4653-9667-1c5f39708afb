import { Component, EventEmitter, Output } from '@angular/core';
import { permissionList } from '@masar/common/constants';
import { isOrigin } from '@masar/common/utils';
import { OrganizationOrigin } from '@masar/common/enums';

@Component({ templateUrl: './dropdown-apps.component.html' })
export class DropdownAppsComponent {
    @Output()
    public done: EventEmitter<boolean> = new EventEmitter<boolean>();

    public apps = [
        {
            title: 'translate_kpis',
            link: ['', 'kpi'],
            icon: 'fa-light fa-chart-bar',
            permissionIdList: [permissionList.kpiRead],
            bg: 'bg-primary-500',
        },
        {
            title: 'translate_operations',
            link: ['', 'operation'],
            icon: 'fa-light fa-cogs',
            permissionIdList: [permissionList.operationRead],
            bg: 'bg-secondary-500',
        },
        {
            title: 'translate_operational_plans',
            link: ['', 'plan'],
            icon: 'fa-light fa-ruler',
            permissionIdList: [permissionList.planRead],
            bg: 'bg-red-500',
        },
        {
            title: 'translate_benchmarks',
            link: ['', 'benchmark'],
            icon: 'fa-light fa-compress-alt',
            permissionIdList: [permissionList.benchmarkRead],
            bg: 'bg-blue-500',
        },
        {
            title: 'translate_teams',
            link: ['', 'team'],
            icon: 'fa-light fa-user-friends',
            permissionIdList: [permissionList.team],
            bg: 'bg-green-500',
        },
        {
            title: 'translate_partners',
            link: ['', 'partner'],
            icon: 'fa-light fa-handshake',
            permissionIdList: [permissionList.partner],
            bg: 'bg-pink-500',
        },
        {
            title: 'translate_services',
            link: ['', 'service'],
            icon: 'fa-light fa-bell-concierge',
            permissionIdList: [permissionList.service],
            bg: 'bg-yellow-500',
        },
        {
            title: 'translate_strategic_goals',
            permissionIdList: [permissionList.strategicGoal],
            link: ['', 'system-list', 'strategic-goal'],
            icon: 'fa-light fa-bullseye',
            bg: 'bg-blue-500',
        },
        {
            title: 'translate_kpi_reports',
            icon: 'fa-light fa-chart-line-up',
            link: ['', 'report', 'kpi'],
            permissionIdList: [permissionList.reportKpi],
            bg: 'bg-primary-500',
        },
        {
            title: 'translate_department_reports',
            icon: 'fa-light fa-chart-line-up',
            link: ['', 'report', 'department', 'result'],
            permissionIdList: [permissionList.reportDepartment],
            bg: 'bg-secondary-500',
        },
        {
            title: 'translate_risk_management',
            icon: 'fa-light fa-bolt',
            link: ['', 'risk'],
            permissionIdList: [permissionList.riskRead],
            bg: 'bg-red-600',
            isHidden: isOrigin([
                OrganizationOrigin.police,
                OrganizationOrigin.injaz,
            ]),
        },
    ];
}
